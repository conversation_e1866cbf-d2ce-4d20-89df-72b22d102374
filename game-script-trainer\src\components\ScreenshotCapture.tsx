import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Paper,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CameraAlt,
  Refresh,
  Download,
  Clear,
  ZoomIn,
  ZoomOut,
  Fullscreen
} from '@mui/icons-material';
import { CloudPhoneConnection } from '../types';
import { captureCloudPhoneScreen } from '../utils/cloudPhoneApi';

interface ScreenshotCaptureProps {
  connection: CloudPhoneConnection;
  currentScreenshot?: string;
  onScreenshotCapture: (screenshot: string) => void;
}

const ScreenshotCapture: React.FC<ScreenshotCaptureProps> = ({
  connection,
  currentScreenshot,
  onScreenshotCapture
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [captureError, setCaptureError] = useState<string>('');
  const [zoom, setZoom] = useState(1);
  const [autoCapture, setAutoCapture] = useState(false);
  const [captureInterval, setCaptureInterval] = useState<NodeJS.Timeout | null>(null);
  const [screenInfo, setScreenInfo] = useState<any>(null);
  const [captureMethod, setCaptureMethod] = useState<string>('');
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 手动截图
  const handleManualCapture = useCallback(async () => {
    if (!connection.isConnected) {
      setCaptureError('请先连接云手机');
      return;
    }

    setIsCapturing(true);
    setCaptureError('');

    try {
      // 调用ADB云手机截图API
      const result = await captureCloudPhoneScreen(
        { ip: connection.ip, port: connection.port, timeout: 15000 },
        { format: 'png', quality: 90, compress: false }
      );

      // 保存屏幕信息和截图方法
      setScreenInfo(result.screenInfo);
      setCaptureMethod('ADB截图');

      // 传递截图数据
      onScreenshotCapture(result.screenshot);

      console.log('截图成功，设备信息:', result.screenInfo);

    } catch (error) {
      setCaptureError(`截图失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('截图失败:', error);
    } finally {
      setIsCapturing(false);
    }
  }, [connection, onScreenshotCapture]);



  // 自动截图
  const toggleAutoCapture = useCallback(() => {
    if (autoCapture) {
      // 停止自动截图
      if (captureInterval) {
        clearInterval(captureInterval);
        setCaptureInterval(null);
      }
      setAutoCapture(false);
    } else {
      // 开始自动截图
      const interval = setInterval(() => {
        handleManualCapture();
      }, 5000); // 每5秒截图一次
      
      setCaptureInterval(interval);
      setAutoCapture(true);
    }
  }, [autoCapture, captureInterval, handleManualCapture]);

  // 下载截图
  const handleDownloadScreenshot = useCallback(() => {
    if (!currentScreenshot) return;

    const link = document.createElement('a');
    link.href = currentScreenshot;
    link.download = `screenshot_${new Date().getTime()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [currentScreenshot]);

  // 清除截图
  const handleClearScreenshot = useCallback(() => {
    onScreenshotCapture('');
    setScreenInfo(null);
    setCaptureMethod('');
  }, [onScreenshotCapture]);

  // 处理鼠标移动，显示坐标
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current || !screenInfo) return;

    const rect = imageRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 计算在原始屏幕上的坐标
    const scaleX = screenInfo.width / rect.width;
    const scaleY = screenInfo.height / rect.height;

    const realX = Math.round(x * scaleX);
    const realY = Math.round(y * scaleY);

    setMousePosition({ x: realX, y: realY });
  }, [screenInfo]);

  // 鼠标离开时清除坐标
  const handleMouseLeave = useCallback(() => {
    setMousePosition(null);
  }, []);

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.25, 0.25));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoom(1);
  }, []);

  // 全屏查看
  const handleFullscreen = useCallback(() => {
    if (imageRef.current) {
      if (imageRef.current.requestFullscreen) {
        imageRef.current.requestFullscreen();
      }
    }
  }, []);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (captureInterval) {
        clearInterval(captureInterval);
      }
    };
  }, [captureInterval]);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        游戏界面截图
      </Typography>

      <Box display="flex" gap={3} flexDirection={{ xs: 'column', md: 'row' }}>
        {/* 控制面板 */}
        <Box flex={{ xs: '1', md: '0 0 33%' }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                截图控制
              </Typography>

              {!connection.isConnected && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  请先连接云手机后再进行截图
                </Alert>
              )}

              {captureError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {captureError}
                </Alert>
              )}

              {screenInfo && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2" component="div">
                    <strong>设备屏幕信息:</strong><br/>
                    分辨率: {screenInfo.width} × {screenInfo.height}<br/>
                    密度: {screenInfo.density} DPI<br/>
                    方向: {screenInfo.orientation}<br/>
                    截图方式: {captureMethod}
                  </Typography>
                </Alert>
              )}

              <Box display="flex" flexDirection="column" gap={2}>
                <Button
                  variant="contained"
                  startIcon={<CameraAlt />}
                  onClick={handleManualCapture}
                  disabled={!connection.isConnected || isCapturing}
                  fullWidth
                >
                  {isCapturing ? '截图中...' : '手动截图'}
                </Button>

                <Button
                  variant={autoCapture ? "contained" : "outlined"}
                  color={autoCapture ? "error" : "primary"}
                  startIcon={<Refresh />}
                  onClick={toggleAutoCapture}
                  disabled={!connection.isConnected}
                  fullWidth
                >
                  {autoCapture ? '停止自动截图' : '开始自动截图'}
                </Button>

                {currentScreenshot && (
                  <>
                    <Button
                      variant="outlined"
                      startIcon={<Download />}
                      onClick={handleDownloadScreenshot}
                      fullWidth
                    >
                      下载截图
                    </Button>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<Clear />}
                      onClick={handleClearScreenshot}
                      fullWidth
                    >
                      清除截图
                    </Button>
                  </>
                )}
              </Box>

              {autoCapture && (
                <Box mt={2}>
                  <Chip
                    label="自动截图进行中 (每5秒)"
                    color="success"
                    variant="outlined"
                  />
                </Box>
              )}
            </CardContent>
          </Card>

          {/* 缩放控制 */}
          {currentScreenshot && (
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  查看控制
                </Typography>

                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Tooltip title="缩小">
                    <IconButton onClick={handleZoomOut} disabled={zoom <= 0.25}>
                      <ZoomOut />
                    </IconButton>
                  </Tooltip>
                  
                  <Chip 
                    label={`${Math.round(zoom * 100)}%`} 
                    onClick={handleResetZoom}
                    clickable
                  />
                  
                  <Tooltip title="放大">
                    <IconButton onClick={handleZoomIn} disabled={zoom >= 3}>
                      <ZoomIn />
                    </IconButton>
                  </Tooltip>
                  
                  <Tooltip title="全屏查看">
                    <IconButton onClick={handleFullscreen}>
                      <Fullscreen />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Typography variant="body2" color="textSecondary">
                  点击百分比重置缩放
                </Typography>
              </CardContent>
            </Card>
          )}
        </Box>

        {/* 截图显示区域 */}
        <Box flex={{ xs: '1', md: '0 0 67%' }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                当前截图
              </Typography>

              <Paper
                ref={containerRef}
                sx={{
                  minHeight: '400px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  overflow: 'auto',
                  border: '2px dashed #ccc',
                  backgroundColor: '#f5f5f5'
                }}
              >
                {currentScreenshot ? (
                  <img
                    ref={imageRef}
                    src={currentScreenshot}
                    alt="游戏截图"
                    onMouseMove={handleMouseMove}
                    onMouseLeave={handleMouseLeave}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      transform: `scale(${zoom})`,
                      transition: 'transform 0.2s ease',
                      cursor: zoom > 1 ? 'move' : 'crosshair'
                    }}
                  />
                ) : (
                  <Box textAlign="center" color="text.secondary">
                    <CameraAlt sx={{ fontSize: 64, mb: 2 }} />
                    <Typography variant="h6">
                      暂无截图
                    </Typography>
                    <Typography variant="body2">
                      点击"手动截图"或"开始自动截图"获取游戏界面
                    </Typography>
                  </Box>
                )}
              </Paper>

              {currentScreenshot && (
                <Box mt={2}>
                  <Typography variant="body2" color="textSecondary">
                    截图时间: {new Date().toLocaleString()}
                  </Typography>
                  {mousePosition && screenInfo && (
                    <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                      鼠标坐标: ({mousePosition.x}, {mousePosition.y}) / 屏幕尺寸: {screenInfo.width}×{screenInfo.height}
                    </Typography>
                  )}
                  {screenInfo && (
                    <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                      💡 移动鼠标查看实时坐标，坐标与云手机屏幕完全一致
                    </Typography>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* 使用提示 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            使用提示
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • 手动截图：点击"手动截图"按钮获取当前游戏界面
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • 自动截图：开启后每5秒自动获取一次截图，便于实时监控
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • 缩放查看：使用缩放控制查看截图细节
          </Typography>
          <Typography variant="body2">
            • 下载保存：可以下载截图到本地进行保存和分析
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ScreenshotCapture;
