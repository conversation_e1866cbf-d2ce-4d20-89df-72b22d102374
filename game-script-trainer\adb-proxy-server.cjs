const express = require('express');
const cors = require('cors');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());

// 执行ADB命令的辅助函数
function executeAdbCommand(deviceId, command) {
  return new Promise((resolve, reject) => {
    const adbCommand = `adb -s ${deviceId} ${command}`;
    console.log('执行ADB命令:', adbCommand);
    
    exec(adbCommand, { encoding: 'buffer', maxBuffer: 10 * 1024 * 1024 }, (error, stdout, stderr) => {
      if (error) {
        console.error('ADB命令执行失败:', error);
        reject(error);
        return;
      }
      
      if (stderr && stderr.length > 0) {
        console.warn('ADB命令警告:', stderr.toString());
      }
      
      resolve(stdout);
    });
  });
}

// ADB Shell命令接口
app.post('/shell', async (req, res) => {
  try {
    const { command, deviceId = '127.0.0.1:59829' } = req.body;
    
    if (!command) {
      return res.status(400).json({ error: '缺少command参数' });
    }
    
    console.log(`执行Shell命令: ${command} (设备: ${deviceId})`);
    
    const result = await executeAdbCommand(deviceId, `shell "${command}"`);
    const output = result.toString('utf8');
    
    res.json({
      success: true,
      output: output,
      command: command,
      deviceId: deviceId
    });
    
  } catch (error) {
    console.error('Shell命令执行失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      command: req.body.command
    });
  }
});

// ADB截图接口
app.post('/screenshot', async (req, res) => {
  try {
    const { deviceId = '127.0.0.1:59829', format = 'png' } = req.body;
    
    console.log(`获取设备截图: ${deviceId}`);
    
    // 执行截图命令
    const screenshotData = await executeAdbCommand(deviceId, 'exec-out screencap -p');
    
    // 转换为base64
    const base64Data = screenshotData.toString('base64');
    const dataUrl = `data:image/${format};base64,${base64Data}`;
    
    res.json({
      success: true,
      screenshot: dataUrl,
      format: format,
      deviceId: deviceId,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('截图失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      deviceId: req.body.deviceId
    });
  }
});

// 获取设备信息接口
app.get('/device/info', async (req, res) => {
  try {
    const { deviceId = '127.0.0.1:59829' } = req.query;
    
    console.log(`获取设备信息: ${deviceId}`);
    
    // 获取设备基本信息
    const [androidVersion, deviceModel, screenSize] = await Promise.all([
      executeAdbCommand(deviceId, 'shell getprop ro.build.version.release'),
      executeAdbCommand(deviceId, 'shell getprop ro.product.model'),
      executeAdbCommand(deviceId, 'shell wm size')
    ]);
    
    res.json({
      success: true,
      deviceInfo: {
        androidVersion: androidVersion.toString('utf8').trim(),
        deviceModel: deviceModel.toString('utf8').trim(),
        screenSize: screenSize.toString('utf8').trim(),
        deviceId: deviceId
      }
    });
    
  } catch (error) {
    console.error('获取设备信息失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      deviceId: req.query.deviceId
    });
  }
});

// 检查ADB连接状态
app.get('/devices', async (req, res) => {
  try {
    console.log('检查ADB设备列表');
    
    const result = await new Promise((resolve, reject) => {
      exec('adb devices', (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }
        resolve(stdout);
      });
    });
    
    const devices = result.toString()
      .split('\n')
      .slice(1) // 跳过标题行
      .filter(line => line.trim() && !line.includes('List of devices'))
      .map(line => {
        const parts = line.trim().split('\t');
        return {
          id: parts[0],
          status: parts[1] || 'unknown'
        };
      });
    
    res.json({
      success: true,
      devices: devices
    });
    
  } catch (error) {
    console.error('获取设备列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 点击坐标接口
app.post('/tap', async (req, res) => {
  try {
    const { x, y, deviceId = '127.0.0.1:59829' } = req.body;
    
    if (x === undefined || y === undefined) {
      return res.status(400).json({ error: '缺少x或y坐标' });
    }
    
    console.log(`点击坐标: (${x}, ${y}) 设备: ${deviceId}`);
    
    await executeAdbCommand(deviceId, `shell input tap ${x} ${y}`);
    
    res.json({
      success: true,
      action: 'tap',
      coordinates: { x, y },
      deviceId: deviceId
    });
    
  } catch (error) {
    console.error('点击操作失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`ADB代理服务器启动成功！`);
  console.log(`服务地址: http://localhost:${PORT}`);
  console.log(`支持的接口:`);
  console.log(`  POST /shell - 执行Shell命令`);
  console.log(`  POST /screenshot - 获取截图`);
  console.log(`  GET /device/info - 获取设备信息`);
  console.log(`  GET /devices - 获取设备列表`);
  console.log(`  POST /tap - 点击坐标`);
  console.log(`\n请确保ADB已安装并且设备已连接！`);
});
