import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert
} from '@mui/material';
import {
  CropFree,
  Check,
  Clear
} from '@mui/icons-material';

import { AreaSelection } from '../types';

interface AreaSelectorProps {
  screenshot: string;
  onAreaSelected: (selection: AreaSelection) => void;
  selectionType: 'main' | 'sub' | 'click';
  instruction: string;
}

interface SelectionRect {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
}

const AreaSelector: React.FC<AreaSelectorProps> = ({
  screenshot,
  onAreaSelected,
  selectionType,
  instruction
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<SelectionRect | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });

  // 初始化画布
  useEffect(() => {
    if (!screenshot || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      // 计算适合容器的尺寸
      const containerWidth = 800;
      const containerHeight = 600;
      
      const imgRatio = img.width / img.height;
      const containerRatio = containerWidth / containerHeight;
      
      let displayWidth, displayHeight;
      
      if (imgRatio > containerRatio) {
        displayWidth = containerWidth;
        displayHeight = containerWidth / imgRatio;
      } else {
        displayHeight = containerHeight;
        displayWidth = containerHeight * imgRatio;
      }
      
      canvas.width = displayWidth;
      canvas.height = displayHeight;
      
      setCanvasSize({ width: displayWidth, height: displayHeight });
      
      // 绘制图片
      ctx.drawImage(img, 0, 0, displayWidth, displayHeight);
      
      imageRef.current = img;
      setImageLoaded(true);
    };
    
    img.src = screenshot;
  }, [screenshot]);

  // 鼠标事件处理
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setIsSelecting(true);
    setCurrentSelection({
      startX: x,
      startY: y,
      endX: x,
      endY: y
    });
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isSelecting || !canvasRef.current || !currentSelection) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setCurrentSelection(prev => prev ? {
      ...prev,
      endX: x,
      endY: y
    } : null);
  }, [isSelecting, currentSelection]);

  const handleMouseUp = useCallback(() => {
    setIsSelecting(false);
  }, []);

  // 绘制选择框
  useEffect(() => {
    if (!canvasRef.current || !imageRef.current || !imageLoaded) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 重新绘制图片
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);
    
    // 绘制选择框
    if (currentSelection) {
      const { startX, startY, endX, endY } = currentSelection;
      const x = Math.min(startX, endX);
      const y = Math.min(startY, endY);
      const width = Math.abs(endX - startX);
      const height = Math.abs(endY - startY);
      
      // 绘制选择框
      ctx.strokeStyle = selectionType === 'main' ? '#1976d2' : selectionType === 'sub' ? '#ff9800' : '#4caf50';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.strokeRect(x, y, width, height);
      
      // 绘制半透明覆盖
      ctx.fillStyle = selectionType === 'main' ? 'rgba(25, 118, 210, 0.1)' : 
                      selectionType === 'sub' ? 'rgba(255, 152, 0, 0.1)' : 
                      'rgba(76, 175, 80, 0.1)';
      ctx.fillRect(x, y, width, height);
      
      // 重置线条样式
      ctx.setLineDash([]);
    }
  }, [currentSelection, imageLoaded, selectionType]);

  // 确认选择
  const handleConfirmSelection = useCallback(() => {
    if (!currentSelection || !imageRef.current || !canvasRef.current) return;
    
    const { startX, startY, endX, endY } = currentSelection;
    const x = Math.min(startX, endX);
    const y = Math.min(startY, endY);
    const width = Math.abs(endX - startX);
    const height = Math.abs(endY - startY);
    
    if (width < 10 || height < 10) {
      alert('选择区域太小，请重新选择');
      return;
    }
    
    // 计算在原始图片中的坐标
    const scaleX = imageRef.current.width / canvasSize.width;
    const scaleY = imageRef.current.height / canvasSize.height;
    
    const originalX = Math.round(x * scaleX);
    const originalY = Math.round(y * scaleY);
    const originalWidth = Math.round(width * scaleX);
    const originalHeight = Math.round(height * scaleY);
    
    // 创建区域截图
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalWidth;
    tempCanvas.height = originalHeight;
    const tempCtx = tempCanvas.getContext('2d');
    
    if (tempCtx) {
      tempCtx.drawImage(
        imageRef.current,
        originalX, originalY, originalWidth, originalHeight,
        0, 0, originalWidth, originalHeight
      );
      
      const areaScreenshot = tempCanvas.toDataURL('image/png');
      
      const selection: AreaSelection = {
        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
        name: '', // 将在后续步骤中命名
        type: selectionType,
        coordinates: {
          x: originalX,
          y: originalY,
          width: originalWidth,
          height: originalHeight
        },
        screenshot: areaScreenshot,
        fullScreenshot: screenshot,
        createdAt: new Date()
      };
      
      onAreaSelected(selection);
    }
  }, [currentSelection, canvasSize, screenshot, selectionType, onAreaSelected]);

  // 取消选择
  const handleCancelSelection = useCallback(() => {
    setCurrentSelection(null);
    setIsSelecting(false);
  }, []);

  return (
    <Box>
      <Alert severity="info" sx={{ mb: 2 }}>
        {instruction}
      </Alert>
      
      <Paper 
        sx={{ 
          p: 2, 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center',
          border: '2px dashed #ccc'
        }}
      >
        {imageLoaded ? (
          <>
            <canvas
              ref={canvasRef}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              style={{
                cursor: isSelecting ? 'crosshair' : 'pointer',
                border: '1px solid #ddd',
                maxWidth: '100%',
                maxHeight: '600px'
              }}
            />
            
            {currentSelection && (
              <Box mt={2} display="flex" gap={2}>
                <Button
                  variant="contained"
                  startIcon={<Check />}
                  onClick={handleConfirmSelection}
                  color="primary"
                >
                  确认选择
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Clear />}
                  onClick={handleCancelSelection}
                >
                  取消
                </Button>
              </Box>
            )}
            
            {currentSelection && (
              <Box mt={1}>
                <Typography variant="body2" color="text.secondary">
                  选择区域: {Math.abs(currentSelection.endX - currentSelection.startX)} × {Math.abs(currentSelection.endY - currentSelection.startY)} 像素
                </Typography>
              </Box>
            )}
          </>
        ) : (
          <Box display="flex" alignItems="center" justifyContent="center" minHeight="200px">
            <Typography variant="body1" color="text.secondary">
              正在加载图片...
            </Typography>
          </Box>
        )}
      </Paper>
      
      <Box mt={2}>
        <Typography variant="body2" color="text.secondary">
          • 用鼠标拖拽选择需要的区域
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • {selectionType === 'main' ? '蓝色' : selectionType === 'sub' ? '橙色' : '绿色'}框表示当前选择类型
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • 选择完成后点击"确认选择"按钮
        </Typography>
      </Box>
    </Box>
  );
};

export default AreaSelector;
