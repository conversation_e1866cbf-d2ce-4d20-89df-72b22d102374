import React from 'react';
import { ThemeProvider, createTheme, CssBaseline, Typography, Container, Paper } from '@mui/material';

const theme = createTheme({
  palette: {
    mode: 'light',
  },
});

function TestApp() {
  console.log('TestApp组件正在渲染...');
  
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h3" gutterBottom color="primary">
            🎯 页面加载成功！
          </Typography>
          <Typography variant="h5" gutterBottom>
            游戏脚本训练器 - 测试版本
          </Typography>
          <Typography variant="body1" paragraph>
            如果你能看到这个消息，说明React应用正在正常运行。
          </Typography>
          <Typography variant="body2" color="text.secondary">
            这是一个简化的测试页面，用于确认应用是否能正常加载。
          </Typography>
        </Paper>
      </Container>
    </ThemeProvider>
  );
}

export default TestApp;
