// 红手指云手机API工具函数

export interface CloudPhoneApiConfig {
  ip: string;
  port: number;
  timeout?: number;
}

export interface ScreenshotOptions {
  format?: 'png' | 'jpg';
  quality?: number;
  compress?: boolean;
  width?: number;
  height?: number;
}

export interface ClickOptions {
  x: number;
  y: number;
  duration?: number;
  pressure?: number;
}

export interface SwipeOptions {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration?: number;
}

// 获取设备屏幕信息
export async function getDeviceScreenInfo(
  config: CloudPhoneApiConfig
): Promise<{ width: number; height: number; density: number; orientation: string }> {
  const { ip, port, timeout = 5000 } = config;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 使用ADB命令获取屏幕信息
    const response = await fetch(`http://${ip}:${port}/shell`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: 'wm size && wm density && dumpsys input | grep "SurfaceOrientation"'
      }),
      signal: controller.signal,
    });

    if (response.ok) {
      const result = await response.json();
      const output = result.output || '';

      // 解析屏幕尺寸 (Physical size: 1080x1920)
      const sizeMatch = output.match(/Physical size: (\d+)x(\d+)/);
      const width = sizeMatch ? parseInt(sizeMatch[1]) : 1080;
      const height = sizeMatch ? parseInt(sizeMatch[2]) : 1920;

      // 解析密度 (Physical density: 480)
      const densityMatch = output.match(/Physical density: (\d+)/);
      const density = densityMatch ? parseInt(densityMatch[1]) : 480;

      // 解析方向
      const orientationMatch = output.match(/SurfaceOrientation: (\d+)/);
      const orientationCode = orientationMatch ? parseInt(orientationMatch[1]) : 0;
      const orientation = ['portrait', 'landscape', 'reverse-portrait', 'reverse-landscape'][orientationCode] || 'portrait';

      return { width, height, density, orientation };
    }
  } catch (error) {
    console.log('获取屏幕信息失败，使用默认值');
  } finally {
    clearTimeout(timeoutId);
  }

  // 返回默认值
  return { width: 1080, height: 1920, density: 480, orientation: 'portrait' };
}

// 云手机截图API - 使用ADB命令确保坐标一致性
export async function captureCloudPhoneScreen(
  config: CloudPhoneApiConfig,
  options: ScreenshotOptions = {}
): Promise<{ screenshot: string; screenInfo: any }> {
  const { ip, port, timeout = 15000 } = config;
  const { format = 'png', quality = 90, compress = false } = options;

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 首先获取屏幕信息
    const screenInfo = await getDeviceScreenInfo(config);
    console.log('设备屏幕信息:', screenInfo);

    // 方法1: 使用ADB screencap命令（推荐，坐标最准确）
    try {
      const adbResponse = await fetch(`http://${ip}:${port}/shell`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: 'screencap -p | base64'
        }),
        signal: controller.signal,
      });

      if (adbResponse.ok) {
        const result = await adbResponse.json();
        if (result.output && result.output.trim()) {
          const base64Data = result.output.trim().replace(/\n/g, '');
          const screenshot = `data:image/png;base64,${base64Data}`;
          console.log('ADB截图成功，屏幕尺寸:', screenInfo.width, 'x', screenInfo.height);
          return { screenshot, screenInfo };
        }
      }
    } catch (error) {
      console.log('ADB screencap失败，尝试其他方法:', error);
    }

    // 方法2: 使用ADB截图到文件再读取
    try {
      const timestamp = Date.now();
      const filename = `/sdcard/screenshot_${timestamp}.png`;

      // 截图到文件
      const captureResponse = await fetch(`http://${ip}:${port}/shell`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: `screencap -p ${filename}`
        }),
        signal: controller.signal,
      });

      if (captureResponse.ok) {
        // 读取文件并转换为base64
        const readResponse = await fetch(`http://${ip}:${port}/shell`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            command: `base64 ${filename} && rm ${filename}`
          }),
          signal: controller.signal,
        });

        if (readResponse.ok) {
          const result = await readResponse.json();
          if (result.output && result.output.trim()) {
            const base64Data = result.output.trim().replace(/\n/g, '');
            const screenshot = `data:image/png;base64,${base64Data}`;
            console.log('ADB文件截图成功');
            return { screenshot, screenInfo };
          }
        }
      }
    } catch (error) {
      console.log('ADB文件截图失败，尝试其他方法:', error);
    }

    // 方法3: 尝试红手指官方API
    try {
      const redFingerUrl = `http://${ip}:${port}/api/v1/screenshot`;
      const response = await fetch(redFingerUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GameScriptTrainer/1.0',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          format,
          quality,
          compress,
          timestamp: Date.now()
        }),
        signal: controller.signal,
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.screenshot) {
          const screenshot = data.screenshot.startsWith('data:')
            ? data.screenshot
            : `data:image/${format};base64,${data.screenshot}`;
          console.log('红手指官方API截图成功');
          return { screenshot, screenInfo };
        }
      }
    } catch (error) {
      console.log('红手指官方API不可用，尝试其他方法');
    }

    // 方法4: 尝试通用截图API
    try {
      const adbUrl = `http://${ip}:${port}/screenshot`;
      const response = await fetch(adbUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/png,image/jpeg,*/*',
        },
        signal: controller.signal,
      });

      if (response.ok) {
        const blob = await response.blob();
        const screenshot = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
        console.log('通用截图API成功');
        return { screenshot, screenInfo };
      }
    } catch (error) {
      console.log('通用截图API不可用');
    }

    // 如果所有方法都失败，抛出错误
    throw new Error(`无法获取截图，请检查：
1. 云手机是否正常运行
2. ADB服务是否启用
3. 网络连接是否正常
4. IP地址和端口是否正确

尝试的方法：
- ADB screencap命令
- ADB文件截图
- 红手指官方API
- 通用截图API

建议：请确保云手机支持ADB连接，端口通常为5555`);

  } finally {
    clearTimeout(timeoutId);
  }
}





// 云手机点击操作
export async function clickOnCloudPhone(
  config: CloudPhoneApiConfig,
  options: ClickOptions
): Promise<boolean> {
  const { ip, port, timeout = 5000 } = config;
  const { x, y, duration = 100, pressure = 1.0 } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试红手指点击API
    const clickUrl = `http://${ip}:${port}/api/v1/input/tap`;
    const response = await fetch(clickUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        x,
        y,
        duration,
        pressure,
        timestamp: Date.now()
      }),
      signal: controller.signal,
    });

    if (response.ok) {
      const result = await response.json();
      return result.success === true;
    }

    // 备用方法：使用ADB input命令
    const shellUrl = `http://${ip}:${port}/shell`;
    const shellResponse = await fetch(shellUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: `input tap ${x} ${y}`
      }),
      signal: controller.signal,
    });

    return shellResponse.ok;

  } catch (error) {
    console.error('云手机点击操作失败:', error);
    return false;
  } finally {
    clearTimeout(timeoutId);
  }
}

// 云手机滑动操作
export async function swipeOnCloudPhone(
  config: CloudPhoneApiConfig,
  options: SwipeOptions
): Promise<boolean> {
  const { ip, port, timeout = 5000 } = config;
  const { startX, startY, endX, endY, duration = 300 } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试红手指滑动API
    const swipeUrl = `http://${ip}:${port}/api/v1/input/swipe`;
    const response = await fetch(swipeUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        startX,
        startY,
        endX,
        endY,
        duration,
        timestamp: Date.now()
      }),
      signal: controller.signal,
    });

    if (response.ok) {
      const result = await response.json();
      return result.success === true;
    }

    // 备用方法：使用ADB input命令
    const shellUrl = `http://${ip}:${port}/shell`;
    const shellResponse = await fetch(shellUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: `input swipe ${startX} ${startY} ${endX} ${endY} ${duration}`
      }),
      signal: controller.signal,
    });

    return shellResponse.ok;

  } catch (error) {
    console.error('云手机滑动操作失败:', error);
    return false;
  } finally {
    clearTimeout(timeoutId);
  }
}

// 测试云手机连接
export async function testCloudPhoneConnection(
  config: CloudPhoneApiConfig
): Promise<{ success: boolean; message: string; deviceInfo?: any }> {
  const { ip, port, timeout = 5000 } = config;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 方法1: 尝试红手指官方API
    try {
      const deviceInfoUrl = `http://${ip}:${port}/api/v1/device/info`;
      const response = await fetch(deviceInfoUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'GameScriptTrainer/1.0',
        },
        signal: controller.signal,
      });

      if (response.ok) {
        const deviceInfo = await response.json();
        return {
          success: true,
          message: '连接成功（红手指API）',
          deviceInfo
        };
      }
    } catch (error) {
      console.log('红手指API测试失败，尝试其他方法');
    }

    // 方法2: 尝试ADB连接测试
    try {
      const adbUrl = `http://${ip}:${port}/status`;
      const adbResponse = await fetch(adbUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: controller.signal,
      });

      if (adbResponse.ok) {
        const status = await adbResponse.json();
        return {
          success: true,
          message: '连接成功（ADB）',
          deviceInfo: status
        };
      }
    } catch (error) {
      console.log('ADB状态测试失败，尝试其他方法');
    }

    // 方法3: 尝试TCP端口连通性测试
    try {
      const isReachable = await testTcpConnection(ip, port, 3000);
      if (isReachable) {
        return {
          success: true,
          message: '连接成功（TCP端口可达）'
        };
      }
    } catch (error) {
      console.log('TCP连接测试失败');
    }

    // 方法4: 尝试简单的HTTP连接测试
    try {
      const pingUrl = `http://${ip}:${port}/`;
      const pingResponse = await fetch(pingUrl, {
        method: 'GET',
        signal: controller.signal,
      });

      // 即使返回404，也说明端口是开放的
      if (pingResponse.status === 404 || pingResponse.ok) {
        return {
          success: true,
          message: '连接成功（HTTP端口开放）'
        };
      }
    } catch (error) {
      console.log('HTTP连接测试失败');
    }

    // 方法5: 尝试WebSocket连接测试
    try {
      const wsTestResult = await testWebSocketConnection(ip, port, 3000);
      if (wsTestResult) {
        return {
          success: true,
          message: '连接成功（WebSocket）'
        };
      }
    } catch (error) {
      console.log('WebSocket连接测试失败');
    }

    return {
      success: false,
      message: `无法连接到 ${ip}:${port}，请检查：\n1. IP地址和端口是否正确\n2. 云手机是否已启动\n3. 网络是否连通\n4. 防火墙设置`
    };

  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        message: '连接超时，请检查网络连接'
      };
    }

    return {
      success: false,
      message: `连接失败: ${error instanceof Error ? error.message : '网络错误'}`
    };
  } finally {
    clearTimeout(timeoutId);
  }
}

// TCP连接测试（通过尝试建立连接来测试端口可达性）
async function testTcpConnection(ip: string, port: number, timeout: number): Promise<boolean> {
  return new Promise((resolve) => {
    // 使用Image对象来测试连接（虽然会失败，但可以检测端口是否开放）
    const img = new Image();
    const timeoutId = setTimeout(() => {
      resolve(false);
    }, timeout);

    // 尝试连接到一个不存在的图片URL来测试端口连通性
    img.onload = () => {
      clearTimeout(timeoutId);
      resolve(true);
    };

    img.onerror = (error: any) => {
      clearTimeout(timeoutId);
      // 如果是网络错误（端口不可达），error.type 通常是 'error'
      // 如果是404错误（端口可达但资源不存在），我们认为连接成功
      if (error && error.type === 'error') {
        // 进一步检查是否是网络连接问题
        const errorMsg = error.message || '';
        if (errorMsg.includes('network') || errorMsg.includes('timeout') || errorMsg.includes('refused')) {
          resolve(false);
        } else {
          resolve(true); // 端口开放，只是资源不存在
        }
      } else {
        resolve(true); // 端口开放
      }
    };

    img.src = `http://${ip}:${port}/test_connection_${Date.now()}.png`;
  });
}

// WebSocket连接测试
async function testWebSocketConnection(ip: string, port: number, timeout: number): Promise<boolean> {
  return new Promise((resolve) => {
    const wsUrl = `ws://${ip}:${port}`;
    let ws: WebSocket;

    const timeoutId = setTimeout(() => {
      if (ws) ws.close();
      resolve(false);
    }, timeout);

    try {
      ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        clearTimeout(timeoutId);
        ws.close();
        resolve(true);
      };

      ws.onerror = () => {
        clearTimeout(timeoutId);
        resolve(false);
      };

      ws.onclose = () => {
        clearTimeout(timeoutId);
      };

    } catch (error) {
      clearTimeout(timeoutId);
      resolve(false);
    }
  });
}
