// 红手指云手机API工具函数

export interface CloudPhoneApiConfig {
  ip: string;
  port: number;
  timeout?: number;
}

export interface ScreenshotOptions {
  format?: 'png' | 'jpg';
  quality?: number;
  compress?: boolean;
  width?: number;
  height?: number;
}

export interface ClickOptions {
  x: number;
  y: number;
  duration?: number;
  pressure?: number;
}

export interface SwipeOptions {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration?: number;
}

// 云手机截图API
export async function captureCloudPhoneScreen(
  config: CloudPhoneApiConfig,
  options: ScreenshotOptions = {}
): Promise<string> {
  const { ip, port, timeout = 10000 } = config;
  const { format = 'png', quality = 90, compress = false } = options;

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 方法1: 尝试红手指官方API
    try {
      const redFingerUrl = `http://${ip}:${port}/api/v1/screenshot`;
      const response = await fetch(redFingerUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GameScriptTrainer/1.0',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          format,
          quality,
          compress,
          timestamp: Date.now()
        }),
        signal: controller.signal,
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.screenshot) {
          return data.screenshot.startsWith('data:') 
            ? data.screenshot 
            : `data:image/${format};base64,${data.screenshot}`;
        }
      }
    } catch (error) {
      console.log('红手指官方API不可用，尝试其他方法');
    }

    // 方法2: 尝试ADB截图API
    try {
      const adbUrl = `http://${ip}:${port}/screenshot`;
      const response = await fetch(adbUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/png,image/jpeg,*/*',
        },
        signal: controller.signal,
      });

      if (response.ok) {
        const blob = await response.blob();
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
      }
    } catch (error) {
      console.log('ADB截图API不可用，尝试shell命令');
    }

    // 方法3: 使用ADB shell命令
    try {
      const shellUrl = `http://${ip}:${port}/shell`;
      const response = await fetch(shellUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: `screencap -p /sdcard/temp_screenshot.${format} && base64 /sdcard/temp_screenshot.${format} && rm /sdcard/temp_screenshot.${format}`
        }),
        signal: controller.signal,
      });

      if (response.ok) {
        const result = await response.json();
        if (result.output && result.output.trim()) {
          return `data:image/${format};base64,${result.output.trim()}`;
        }
      }
    } catch (error) {
      console.log('Shell命令不可用，尝试WebSocket');
    }

    // 方法4: 使用WebSocket实时流
    return await captureViaWebSocket(ip, port, timeout);

  } finally {
    clearTimeout(timeoutId);
  }
}

// 通过WebSocket获取截图
async function captureViaWebSocket(ip: string, port: number, timeout: number): Promise<string> {
  return new Promise((resolve, reject) => {
    const wsUrl = `ws://${ip}:${port}/screen`;
    let ws: WebSocket;
    
    const timeoutId = setTimeout(() => {
      if (ws) ws.close();
      reject(new Error('WebSocket连接超时'));
    }, timeout);

    try {
      ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('WebSocket连接已建立');
        ws.send(JSON.stringify({ 
          action: 'capture_screen',
          format: 'png',
          timestamp: Date.now()
        }));
      };

      ws.onmessage = (event) => {
        clearTimeout(timeoutId);
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'screenshot' && data.image) {
            ws.close();
            const imageData = data.image.startsWith('data:') 
              ? data.image 
              : `data:image/png;base64,${data.image}`;
            resolve(imageData);
          } else {
            reject(new Error('无效的截图数据格式'));
          }
        } catch (error) {
          reject(new Error('解析WebSocket数据失败'));
        }
      };

      ws.onerror = (error) => {
        clearTimeout(timeoutId);
        console.error('WebSocket错误:', error);
        reject(new Error('WebSocket连接失败'));
      };

      ws.onclose = (event) => {
        clearTimeout(timeoutId);
        if (event.code !== 1000) {
          reject(new Error(`WebSocket连接关闭: ${event.code} ${event.reason}`));
        }
      };

    } catch (error) {
      clearTimeout(timeoutId);
      reject(new Error(`创建WebSocket连接失败: ${error}`));
    }
  });
}

// 云手机点击操作
export async function clickOnCloudPhone(
  config: CloudPhoneApiConfig,
  options: ClickOptions
): Promise<boolean> {
  const { ip, port, timeout = 5000 } = config;
  const { x, y, duration = 100, pressure = 1.0 } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试红手指点击API
    const clickUrl = `http://${ip}:${port}/api/v1/input/tap`;
    const response = await fetch(clickUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        x,
        y,
        duration,
        pressure,
        timestamp: Date.now()
      }),
      signal: controller.signal,
    });

    if (response.ok) {
      const result = await response.json();
      return result.success === true;
    }

    // 备用方法：使用ADB input命令
    const shellUrl = `http://${ip}:${port}/shell`;
    const shellResponse = await fetch(shellUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: `input tap ${x} ${y}`
      }),
      signal: controller.signal,
    });

    return shellResponse.ok;

  } catch (error) {
    console.error('云手机点击操作失败:', error);
    return false;
  } finally {
    clearTimeout(timeoutId);
  }
}

// 云手机滑动操作
export async function swipeOnCloudPhone(
  config: CloudPhoneApiConfig,
  options: SwipeOptions
): Promise<boolean> {
  const { ip, port, timeout = 5000 } = config;
  const { startX, startY, endX, endY, duration = 300 } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试红手指滑动API
    const swipeUrl = `http://${ip}:${port}/api/v1/input/swipe`;
    const response = await fetch(swipeUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        startX,
        startY,
        endX,
        endY,
        duration,
        timestamp: Date.now()
      }),
      signal: controller.signal,
    });

    if (response.ok) {
      const result = await response.json();
      return result.success === true;
    }

    // 备用方法：使用ADB input命令
    const shellUrl = `http://${ip}:${port}/shell`;
    const shellResponse = await fetch(shellUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: `input swipe ${startX} ${startY} ${endX} ${endY} ${duration}`
      }),
      signal: controller.signal,
    });

    return shellResponse.ok;

  } catch (error) {
    console.error('云手机滑动操作失败:', error);
    return false;
  } finally {
    clearTimeout(timeoutId);
  }
}

// 测试云手机连接
export async function testCloudPhoneConnection(
  config: CloudPhoneApiConfig
): Promise<{ success: boolean; message: string; deviceInfo?: any }> {
  const { ip, port, timeout = 5000 } = config;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试获取设备信息
    const deviceInfoUrl = `http://${ip}:${port}/api/v1/device/info`;
    const response = await fetch(deviceInfoUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: controller.signal,
    });

    if (response.ok) {
      const deviceInfo = await response.json();
      return {
        success: true,
        message: '连接成功',
        deviceInfo
      };
    }

    // 备用测试：简单的ping测试
    const pingUrl = `http://${ip}:${port}/ping`;
    const pingResponse = await fetch(pingUrl, {
      method: 'GET',
      signal: controller.signal,
    });

    if (pingResponse.ok) {
      return {
        success: true,
        message: '连接成功（基础连接）'
      };
    }

    return {
      success: false,
      message: '无法连接到云手机服务'
    };

  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        message: '连接超时'
      };
    }
    
    return {
      success: false,
      message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  } finally {
    clearTimeout(timeoutId);
  }
}
