# 游戏脚本训练器

一个专为红手指云手机设计的游戏自动化脚本开发工具，支持界面识别、坐标获取、颜色识别和文字识别等功能。

## 功能特性

### 🔗 云手机连接
- 支持红手指云手机IP端口连接
- 实时连接状态监控
- 心跳检测机制
- 连接测试功能

### 📸 截图功能
- 手动截图获取游戏界面
- 自动截图（每5秒）
- 截图缩放查看
- 截图下载保存
- 全屏查看模式

### 🖱️ 鼠标交互
- 鼠标涂抹区域识别
- 自动获取坐标信息
- 颜色识别（RGB、HEX、HSL）
- 文字识别（OCR）
- 区域边界框计算

### 🎯 界面识别系统
- 主模块创建和管理
- 子模块识别
- 优先级设置
- 文件夹结构管理
- 界面截图保存

## 核心逻辑

### 三部分逻辑架构

1. **逻辑1：界面识别**
   - 告诉系统当前界面需要做什么
   - 通过截图识别当前游戏界面
   - 匹配已保存的主模块

2. **逻辑2：脚本执行**
   - 流水线式点击操作
   - 从当前界面点击到目标位置
   - 支持多步骤操作序列

3. **逻辑3：界面结束**
   - 每个界面都有关闭按钮
   - 作为界面操作的结束标志
   - 返回上级界面或退出

### 文件夹结构

```
主模块文件夹/
├── 界面截图.png          # 主界面识别图片
├── 子模块1/
│   ├── 按钮截图.png      # 子模块识别图片
│   └── 配置.json         # 坐标、颜色等信息
├── 子模块2/
│   ├── 按钮截图.png
│   └── 配置.json
└── 配置.json             # 主模块配置信息
```

## 使用流程

### 1. 连接云手机
1. 打开"云手机连接"标签页
2. 输入红手指云手机的IP地址和端口号
3. 点击"测试连接"验证网络连通性
4. 点击"连接"建立连接

### 2. 获取游戏截图
1. 切换到"截图功能"标签页
2. 点击"手动截图"获取当前游戏界面
3. 或开启"自动截图"进行实时监控

### 3. 识别界面元素
1. 在"鼠标交互"标签页中
2. 在截图上涂抹需要识别的区域
3. 系统自动识别坐标、颜色和文字
4. 保存识别结果供后续使用

### 4. 创建界面模块
1. 在"界面识别"标签页中
2. 点击"发现新界面"截取界面
3. 为界面命名并设置优先级
4. 添加子模块（按钮、文字等）

## 技术栈

- **前端框架**: React 18 + TypeScript
- **UI组件库**: Material-UI (MUI)
- **画布操作**: Fabric.js
- **截图功能**: html2canvas
- **HTTP请求**: Axios
- **构建工具**: Vite
- **样式方案**: CSS-in-JS (Emotion)

## 项目结构

```
src/
├── components/           # React组件
│   ├── CloudPhoneConnection.tsx    # 云手机连接
│   ├── ScreenshotCapture.tsx       # 截图功能
│   ├── MouseInteraction.tsx        # 鼠标交互
│   └── InterfaceRecognition.tsx    # 界面识别
├── types/               # TypeScript类型定义
│   └── index.ts
├── utils/               # 工具函数
│   ├── dataManager.ts   # 数据管理
│   └── imageProcessor.ts # 图像处理
├── App.tsx              # 主应用组件
├── main.tsx             # 应用入口
└── index.css            # 全局样式
```

## 开发指南

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 数据存储

应用使用浏览器的localStorage进行数据持久化：
- 主模块配置自动保存
- 识别区域信息保存
- 云手机连接配置保存
- 每30秒自动保存一次

## 扩展功能

### 计划中的功能
- [ ] 脚本录制和回放
- [ ] 条件判断和循环
- [ ] 多设备管理
- [ ] 脚本分享和导入
- [ ] 性能监控和日志
- [ ] 云端同步

### API集成
- [ ] 真实的云手机控制API
- [ ] 专业OCR服务集成
- [ ] 图像相似度比较算法
- [ ] 机器学习界面识别

## 注意事项

1. **网络连接**: 确保与云手机网络连通
2. **权限设置**: 云手机需要开启相应的控制权限
3. **性能考虑**: 大量截图可能占用较多内存
4. **数据备份**: 定期导出重要的模块配置

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
