// 坐标信息
export interface Coordinate {
  x: number;
  y: number;
}

// 颜色信息
export interface ColorInfo {
  hex: string;
  rgb: {
    r: number;
    g: number;
    b: number;
  };
  hsl: {
    h: number;
    s: number;
    l: number;
  };
}

// 文字识别信息
export interface TextInfo {
  text: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 子模块信息
export interface SubModule {
  id: string;
  name: string;
  folderName: string;
  color: ColorInfo;
  coordinate: Coordinate;
  screenshot: string; // base64 图片数据
  textInfo?: TextInfo;
  createdAt: Date;
  updatedAt: Date;
}

// 主模块信息
export interface MainModule {
  id: string;
  name: string;
  folderName: string;
  priority: number; // 优先级，数字越小优先级越高
  screenshot: string; // base64 图片数据
  subModules: SubModule[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean; // 当前是否为活跃界面
}

// 云手机连接信息
export interface CloudPhoneConnection {
  ip: string;
  port: number;
  isConnected: boolean;
  connectionTime?: Date;
  lastHeartbeat?: Date;
}

// 截图区域信息
export interface ScreenshotArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 鼠标涂抹区域信息
export interface MousePaintArea {
  id: string;
  coordinates: Coordinate[];
  color: ColorInfo;
  text?: TextInfo;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 界面识别结果
export interface InterfaceRecognitionResult {
  mainModule?: MainModule;
  subModules: SubModule[];
  confidence: number;
  timestamp: Date;
}

// 脚本执行步骤
export interface ScriptStep {
  id: string;
  type: 'click' | 'wait' | 'screenshot' | 'text_input' | 'swipe';
  coordinate?: Coordinate;
  text?: string;
  waitTime?: number;
  description: string;
}

// 脚本流水线
export interface ScriptPipeline {
  id: string;
  name: string;
  description: string;
  steps: ScriptStep[];
  targetModule: string; // 目标主模块ID
  createdAt: Date;
  updatedAt: Date;
}

// 工作流程步骤
export type WorkflowStep =
  | 'screenshot'      // 截图游戏界面
  | 'select_main'     // 选择主界面区域
  | 'create_main'     // 创建主界面模块
  | 'select_sub'      // 选择子界面模块（可选）
  | 'create_sub'      // 创建子界面模块
  | 'create_click'    // 创建默认点击模块
  | 'complete';       // 完成

// 区域选择信息
export interface AreaSelection {
  id: string;
  name: string;
  type: 'main' | 'sub' | 'click';
  coordinates: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  screenshot: string; // 区域截图的base64
  fullScreenshot: string; // 完整截图的base64
  color?: ColorInfo;
  text?: TextInfo;
  createdAt: Date;
}

// 点击模块信息
export interface ClickModule {
  id: string;
  name: string;
  type: 'coordinate' | 'image' | 'text' | 'color';
  coordinate?: Coordinate;
  targetImage?: string; // 目标图像的base64
  targetText?: string;
  targetColor?: ColorInfo;
  tolerance?: number; // 匹配容差
  description: string;
  parentModuleId: string; // 所属主模块ID
  createdAt: Date;
  updatedAt: Date;
}

// 工作流程状态
export interface WorkflowState {
  currentStep: WorkflowStep;
  currentScreenshot?: string;
  mainAreaSelection?: AreaSelection;
  subAreaSelections: AreaSelection[];
  clickModules: ClickModule[];
  isProcessing: boolean;
  error?: string;
}

// 应用状态
export interface AppState {
  cloudPhone: CloudPhoneConnection;
  currentScreenshot?: string;
  mainModules: MainModule[];
  currentRecognition?: InterfaceRecognitionResult;
  mousePaintAreas: MousePaintArea[];
  selectedModule?: MainModule;
  isCapturing: boolean;
  isRecognizing: boolean;
  workflow: WorkflowState;
}
