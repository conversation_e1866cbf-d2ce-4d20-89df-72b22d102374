import React, { useState, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Paper,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Folder,
  Delete,
  Visibility,
  Download,
  Upload,
  Info,
  FolderOpen
} from '@mui/icons-material';

import { folderManager } from '../utils/folderManager';

interface ModuleManagerProps {
  onRefresh: () => void;
}

const ModuleManager: React.FC<ModuleManagerProps> = ({ onRefresh }) => {
  const [modules, setModules] = useState(folderManager.loadAllModules());
  const [selectedModule, setSelectedModule] = useState<any>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showStructureDialog, setShowStructureDialog] = useState(false);
  const [folderStructure, setFolderStructure] = useState('');

  // 刷新模块列表
  const handleRefresh = useCallback(() => {
    setModules(folderManager.loadAllModules());
    onRefresh();
  }, [onRefresh]);

  // 查看模块详情
  const handleViewDetails = useCallback((module: any) => {
    setSelectedModule(module);
    setShowDetailsDialog(true);
  }, []);

  // 查看文件夹结构
  const handleViewStructure = useCallback((module: any) => {
    const structure = folderManager.getFolderStructurePreview(module.name);
    setFolderStructure(structure);
    setShowStructureDialog(true);
  }, []);

  // 删除模块
  const handleDeleteModule = useCallback(async (moduleId: string, moduleName: string) => {
    if (window.confirm(`确定要删除模块 "${moduleName}" 吗？此操作不可恢复。`)) {
      const success = await folderManager.deleteModule(moduleId);
      if (success) {
        handleRefresh();
        alert('模块删除成功');
      } else {
        alert('删除失败');
      }
    }
  }, [handleRefresh]);

  // 导出模块
  const handleExportModule = useCallback((moduleId: string, moduleName: string) => {
    const configJson = folderManager.exportModule(moduleId);
    if (configJson) {
      const blob = new Blob([configJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${moduleName}_config.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  }, []);

  // 导入模块
  const handleImportModule = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = async (event) => {
          const configJson = event.target?.result as string;
          const result = await folderManager.importModule(configJson);
          if (result.success) {
            handleRefresh();
            alert('模块导入成功');
          } else {
            alert(`导入失败: ${result.error}`);
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [handleRefresh]);

  const stats = folderManager.getModuleStats();

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        模块管理
      </Typography>

      {/* 统计信息 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          统计信息
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} md={3}>
            <Typography variant="body2" color="text.secondary">主模块</Typography>
            <Typography variant="h6">{stats.totalModules}</Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="body2" color="text.secondary">子模块</Typography>
            <Typography variant="h6">{stats.totalSubModules}</Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="body2" color="text.secondary">点击模块</Typography>
            <Typography variant="h6">{stats.totalClickModules}</Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="body2" color="text.secondary">最后创建</Typography>
            <Typography variant="body2">
              {stats.lastCreated ? stats.lastCreated.toLocaleDateString() : '无'}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* 操作按钮 */}
      <Box mb={3} display="flex" gap={2}>
        <Button
          variant="outlined"
          startIcon={<Upload />}
          onClick={handleImportModule}
        >
          导入模块
        </Button>
        <Button
          variant="outlined"
          onClick={handleRefresh}
        >
          刷新列表
        </Button>
      </Box>

      {/* 模块列表 */}
      {modules.length > 0 ? (
        <Grid container spacing={2}>
          {modules.map((module) => (
            <Grid item xs={12} md={6} lg={4} key={module.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">{module.name}</Typography>
                    <Chip 
                      label={`优先级: ${module.priority || 0}`} 
                      size="small" 
                      color="primary"
                    />
                  </Box>
                  
                  <img
                    src={module.screenshot}
                    alt={module.name}
                    style={{
                      width: '100%',
                      height: '120px',
                      objectFit: 'cover',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}
                  />
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    子模块: {module.subModules?.length || 0}
                  </Typography>
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    点击模块: {module.clickModules?.length || 0}
                  </Typography>
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    创建时间: {new Date(module.createdAt).toLocaleString()}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Tooltip title="查看详情">
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetails(module)}
                      >
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="文件夹结构">
                      <IconButton
                        size="small"
                        onClick={() => handleViewStructure(module)}
                      >
                        <FolderOpen />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="导出配置">
                      <IconButton
                        size="small"
                        onClick={() => handleExportModule(module.id, module.name)}
                      >
                        <Download />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="删除模块">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteModule(module.id, module.name)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info">
          还没有创建任何模块。请使用"脚本工作流程"标签页创建第一个模块。
        </Alert>
      )}

      {/* 模块详情对话框 */}
      <Dialog 
        open={showDetailsDialog} 
        onClose={() => setShowDetailsDialog(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>模块详情 - {selectedModule?.name}</DialogTitle>
        <DialogContent>
          {selectedModule && (
            <Box>
              <Typography variant="h6" gutterBottom>基本信息</Typography>
              <Typography variant="body2" gutterBottom>
                ID: {selectedModule.id}
              </Typography>
              <Typography variant="body2" gutterBottom>
                坐标: ({selectedModule.coordinates.x}, {selectedModule.coordinates.y})
              </Typography>
              <Typography variant="body2" gutterBottom>
                大小: {selectedModule.coordinates.width} × {selectedModule.coordinates.height}
              </Typography>
              <Typography variant="body2" gutterBottom>
                文件夹路径: {selectedModule.folderPath}
              </Typography>

              {selectedModule.subModules && selectedModule.subModules.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    子模块 ({selectedModule.subModules.length})
                  </Typography>
                  {selectedModule.subModules.map((sub: any, index: number) => (
                    <Paper key={index} sx={{ p: 2, mb: 1 }}>
                      <Typography variant="subtitle2">{sub.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        坐标: ({sub.coordinates.x}, {sub.coordinates.y})
                      </Typography>
                    </Paper>
                  ))}
                </>
              )}

              {selectedModule.clickModules && selectedModule.clickModules.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    点击模块 ({selectedModule.clickModules.length})
                  </Typography>
                  {selectedModule.clickModules.map((click: any, index: number) => (
                    <Paper key={index} sx={{ p: 2, mb: 1 }}>
                      <Typography variant="subtitle2">{click.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        类型: {click.type}
                      </Typography>
                      {click.coordinate && (
                        <Typography variant="body2" color="text.secondary">
                          坐标: ({click.coordinate.x}, {click.coordinate.y})
                        </Typography>
                      )}
                    </Paper>
                  ))}
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetailsDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 文件夹结构对话框 */}
      <Dialog 
        open={showStructureDialog} 
        onClose={() => setShowStructureDialog(false)} 
        maxWidth="sm" 
        fullWidth
      >
        <DialogTitle>文件夹结构预览</DialogTitle>
        <DialogContent>
          <Paper sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
            <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
              {folderStructure}
            </Typography>
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowStructureDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ModuleManager;
