import React, { useState, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  Alert,
  Chip,
  Grid
} from '@mui/material';
import {
  CameraAlt,
  Add,
  Visibility,
  Edit,
  Delete
} from '@mui/icons-material';
import { MainModule, SubModule } from '../types';
import { captureScreenshot } from '../utils/imageProcessor';
import { createMainModule, createSubModule } from '../utils/dataManager';
import ModuleManager from './ModuleManager';

interface InterfaceRecognitionProps {
  modules: MainModule[];
  onModulesChange: (modules: MainModule[]) => void;
  currentScreenshot?: string;
  onScreenshotCapture: (screenshot: string) => void;
}

const InterfaceRecognition: React.FC<InterfaceRecognitionProps> = ({
  modules,
  onModulesChange,
  currentScreenshot,
  onScreenshotCapture
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [newModuleName, setNewModuleName] = useState('');
  const [newModulePriority, setNewModulePriority] = useState(0);
  const [capturedScreenshot, setCapturedScreenshot] = useState<string>('');
  const [selectedModule, setSelectedModule] = useState<MainModule | null>(null);
  const [showSubModuleDialog, setShowSubModuleDialog] = useState(false);
  const [newSubModuleName, setNewSubModuleName] = useState('');

  // 截取屏幕截图
  const handleCaptureScreenshot = useCallback(async () => {
    setIsCapturing(true);
    try {
      const screenshot = await captureScreenshot();
      setCapturedScreenshot(screenshot);
      onScreenshotCapture(screenshot);
      setShowNameDialog(true);
    } catch (error) {
      console.error('截图失败:', error);
    } finally {
      setIsCapturing(false);
    }
  }, [onScreenshotCapture]);

  // 创建新的主模块
  const handleCreateMainModule = useCallback(() => {
    if (!newModuleName.trim() || !capturedScreenshot) return;

    const newModule = createMainModule(
      newModuleName.trim(),
      capturedScreenshot,
      newModulePriority
    );

    const updatedModules = [...modules, newModule];
    onModulesChange(updatedModules);

    // 重置状态
    setNewModuleName('');
    setNewModulePriority(0);
    setCapturedScreenshot('');
    setShowNameDialog(false);
  }, [newModuleName, capturedScreenshot, newModulePriority, modules, onModulesChange]);

  // 创建子模块
  const handleCreateSubModule = useCallback(() => {
    if (!selectedModule || !newSubModuleName.trim() || !currentScreenshot) return;

    const newSubModule = createSubModule(
      newSubModuleName.trim(),
      currentScreenshot,
      { hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, hsl: { h: 0, s: 0, l: 0 } },
      { x: 0, y: 0 }
    );

    const updatedModules = modules.map(module => {
      if (module.id === selectedModule.id) {
        return {
          ...module,
          subModules: [...module.subModules, newSubModule],
          updatedAt: new Date()
        };
      }
      return module;
    });

    onModulesChange(updatedModules);
    setNewSubModuleName('');
    setShowSubModuleDialog(false);
    setSelectedModule(null);
  }, [selectedModule, newSubModuleName, currentScreenshot, modules, onModulesChange]);

  // 删除主模块
  const handleDeleteModule = useCallback((moduleId: string) => {
    const updatedModules = modules.filter(module => module.id !== moduleId);
    onModulesChange(updatedModules);
  }, [modules, onModulesChange]);

  // 更新模块优先级
  const handleUpdatePriority = useCallback((moduleId: string, newPriority: number) => {
    const updatedModules = modules.map(module => {
      if (module.id === moduleId) {
        return { ...module, priority: newPriority, updatedAt: new Date() };
      }
      return module;
    });
    onModulesChange(updatedModules);
  }, [modules, onModulesChange]);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        界面识别系统
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        推荐使用"脚本工作流程"标签页来创建完整的游戏脚本模块。这里显示已创建的模块管理功能。
      </Alert>

      <ModuleManager onRefresh={() => {}} />

      {/* 截图按钮 */}
      <Box mb={3}>
        <Button
          variant="contained"
          startIcon={<CameraAlt />}
          onClick={handleCaptureScreenshot}
          disabled={isCapturing}
          size="large"
        >
          {isCapturing ? '正在截图...' : '发现新界面'}
        </Button>
      </Box>

      {/* 当前截图预览 */}
      {currentScreenshot && (
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            当前截图
          </Typography>
          <img
            src={currentScreenshot}
            alt="当前截图"
            style={{
              maxWidth: '100%',
              maxHeight: '300px',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          />
        </Box>
      )}

      {/* 主模块列表 */}
      <Typography variant="h6" gutterBottom>
        主模块列表 ({modules.length})
      </Typography>
      
      <Grid container spacing={2}>
        {modules
          .sort((a, b) => a.priority - b.priority)
          .map((module) => (
            <Grid item xs={12} md={6} lg={4} key={module.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">{module.name}</Typography>
                    <Chip 
                      label={`优先级: ${module.priority}`} 
                      size="small" 
                      color="primary"
                    />
                  </Box>
                  
                  <img
                    src={module.screenshot}
                    alt={module.name}
                    style={{
                      width: '100%',
                      height: '120px',
                      objectFit: 'cover',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}
                  />
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    子模块: {module.subModules.length}
                  </Typography>
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    创建时间: {module.createdAt.toLocaleString()}
                  </Typography>

                  {/* 优先级调整 */}
                  <Box mt={2}>
                    <Typography variant="body2" gutterBottom>
                      优先级调整
                    </Typography>
                    <Slider
                      value={module.priority}
                      onChange={(_, value) => handleUpdatePriority(module.id, value as number)}
                      min={0}
                      max={10}
                      step={1}
                      marks
                      valueLabelDisplay="auto"
                    />
                  </Box>

                  <Box mt={2} display="flex" gap={1}>
                    <Button
                      size="small"
                      startIcon={<Add />}
                      onClick={() => {
                        setSelectedModule(module);
                        setShowSubModuleDialog(true);
                      }}
                    >
                      添加子模块
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Delete />}
                      color="error"
                      onClick={() => handleDeleteModule(module.id)}
                    >
                      删除
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
      </Grid>

      {modules.length === 0 && (
        <Alert severity="info">
          还没有创建任何主模块。点击"发现新界面"按钮开始创建第一个主模块。
        </Alert>
      )}

      {/* 创建主模块对话框 */}
      <Dialog open={showNameDialog} onClose={() => setShowNameDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>为新界面命名</DialogTitle>
        <DialogContent>
          {capturedScreenshot && (
            <Box mb={2}>
              <img
                src={capturedScreenshot}
                alt="截图预览"
                style={{
                  width: '100%',
                  maxHeight: '200px',
                  objectFit: 'contain',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              />
            </Box>
          )}
          <TextField
            autoFocus
            margin="dense"
            label="界面名称"
            fullWidth
            variant="outlined"
            value={newModuleName}
            onChange={(e) => setNewModuleName(e.target.value)}
            placeholder="例如：首页、登录页、设置页"
          />
          <Box mt={2}>
            <Typography gutterBottom>优先级 (数字越小优先级越高)</Typography>
            <Slider
              value={newModulePriority}
              onChange={(_, value) => setNewModulePriority(value as number)}
              min={0}
              max={10}
              step={1}
              marks
              valueLabelDisplay="auto"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNameDialog(false)}>取消</Button>
          <Button 
            onClick={handleCreateMainModule}
            variant="contained"
            disabled={!newModuleName.trim()}
          >
            创建主模块
          </Button>
        </DialogActions>
      </Dialog>

      {/* 创建子模块对话框 */}
      <Dialog open={showSubModuleDialog} onClose={() => setShowSubModuleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>为 "{selectedModule?.name}" 添加子模块</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="子模块名称"
            fullWidth
            variant="outlined"
            value={newSubModuleName}
            onChange={(e) => setNewSubModuleName(e.target.value)}
            placeholder="例如：开始按钮、设置按钮、返回按钮"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSubModuleDialog(false)}>取消</Button>
          <Button 
            onClick={handleCreateSubModule}
            variant="contained"
            disabled={!newSubModuleName.trim()}
          >
            创建子模块
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InterfaceRecognition;
