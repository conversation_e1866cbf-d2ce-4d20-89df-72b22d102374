import { AreaSelection, ClickModule, MainModule, SubModule } from '../types';

// 文件夹管理工具类
export class FolderManager {
  private static instance: FolderManager;
  private baseDir = 'game_script_modules';

  static getInstance(): FolderManager {
    if (!FolderManager.instance) {
      FolderManager.instance = new FolderManager();
    }
    return FolderManager.instance;
  }

  // 创建安全的文件夹名称
  private createSafeFolderName(name: string): string {
    return name
      .replace(/[^\w\u4e00-\u9fa5\s]/g, '') // 只保留字母、数字、中文和空格
      .replace(/\s+/g, '_') // 空格替换为下划线
      .toLowerCase();
  }

  // 保存主界面模块
  async saveMainModule(
    mainArea: AreaSelection,
    subAreas: AreaSelection[],
    clickModules: ClickModule[]
  ): Promise<{ success: boolean; folderPath: string; error?: string }> {
    try {
      const folderName = this.createSafeFolderName(mainArea.name);
      const folderPath = `${this.baseDir}/${folderName}`;

      // 创建主模块配置
      const mainModuleConfig = {
        id: mainArea.id,
        name: mainArea.name,
        type: 'main',
        coordinates: mainArea.coordinates,
        screenshot: mainArea.screenshot,
        fullScreenshot: mainArea.fullScreenshot,
        createdAt: mainArea.createdAt,
        subModules: subAreas.map(sub => ({
          id: sub.id,
          name: sub.name,
          coordinates: sub.coordinates,
          screenshot: sub.screenshot,
          color: sub.color,
          text: sub.text,
          createdAt: sub.createdAt
        })),
        clickModules: clickModules,
        folderPath: folderPath
      };

      // 保存到localStorage（在实际应用中这里会保存到文件系统）
      const existingModules = this.loadAllModules();
      const updatedModules = existingModules.filter(m => m.id !== mainArea.id);
      updatedModules.push(mainModuleConfig);
      
      localStorage.setItem('game_script_modules', JSON.stringify(updatedModules));

      // 模拟文件夹创建和文件保存
      await this.simulateFileOperations(folderPath, mainModuleConfig, subAreas, clickModules);

      console.log(`主模块已保存到: ${folderPath}`);
      
      return {
        success: true,
        folderPath: folderPath
      };

    } catch (error) {
      console.error('保存主模块失败:', error);
      return {
        success: false,
        folderPath: '',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 模拟文件操作（在实际应用中会调用真实的文件系统API）
  private async simulateFileOperations(
    folderPath: string,
    mainConfig: any,
    subAreas: AreaSelection[],
    clickModules: ClickModule[]
  ): Promise<void> {
    console.log(`创建文件夹: ${folderPath}`);
    
    // 保存主界面截图
    console.log(`保存主界面截图: ${folderPath}/main_screenshot.png`);
    
    // 保存主界面配置
    console.log(`保存主界面配置: ${folderPath}/config.json`);
    
    // 为每个子模块创建文件夹
    for (const subArea of subAreas) {
      const subFolderName = this.createSafeFolderName(subArea.name);
      const subFolderPath = `${folderPath}/${subFolderName}`;
      
      console.log(`创建子模块文件夹: ${subFolderPath}`);
      console.log(`保存子模块截图: ${subFolderPath}/screenshot.png`);
      console.log(`保存子模块配置: ${subFolderPath}/config.json`);
    }
    
    // 创建点击模块文件夹
    if (clickModules.length > 0) {
      const clickFolderPath = `${folderPath}/click_modules`;
      console.log(`创建点击模块文件夹: ${clickFolderPath}`);
      
      for (const clickModule of clickModules) {
        const clickFileName = this.createSafeFolderName(clickModule.name);
        console.log(`保存点击模块配置: ${clickFolderPath}/${clickFileName}.json`);
      }
    }
  }

  // 加载所有模块
  loadAllModules(): any[] {
    try {
      const data = localStorage.getItem('game_script_modules');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('加载模块失败:', error);
      return [];
    }
  }

  // 删除模块
  async deleteModule(moduleId: string): Promise<boolean> {
    try {
      const modules = this.loadAllModules();
      const updatedModules = modules.filter(m => m.id !== moduleId);
      localStorage.setItem('game_script_modules', JSON.stringify(updatedModules));
      
      console.log(`模块 ${moduleId} 已删除`);
      return true;
    } catch (error) {
      console.error('删除模块失败:', error);
      return false;
    }
  }

  // 导出模块配置
  exportModule(moduleId: string): string | null {
    try {
      const modules = this.loadAllModules();
      const module = modules.find(m => m.id === moduleId);
      
      if (module) {
        return JSON.stringify(module, null, 2);
      }
      
      return null;
    } catch (error) {
      console.error('导出模块失败:', error);
      return null;
    }
  }

  // 导入模块配置
  async importModule(configJson: string): Promise<{ success: boolean; error?: string }> {
    try {
      const moduleConfig = JSON.parse(configJson);
      
      // 验证配置格式
      if (!moduleConfig.id || !moduleConfig.name || !moduleConfig.coordinates) {
        throw new Error('无效的模块配置格式');
      }
      
      const modules = this.loadAllModules();
      const existingIndex = modules.findIndex(m => m.id === moduleConfig.id);
      
      if (existingIndex >= 0) {
        modules[existingIndex] = moduleConfig;
      } else {
        modules.push(moduleConfig);
      }
      
      localStorage.setItem('game_script_modules', JSON.stringify(modules));
      
      console.log(`模块 ${moduleConfig.name} 导入成功`);
      return { success: true };
      
    } catch (error) {
      console.error('导入模块失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 获取模块统计信息
  getModuleStats(): {
    totalModules: number;
    totalSubModules: number;
    totalClickModules: number;
    lastCreated?: Date;
  } {
    const modules = this.loadAllModules();
    
    let totalSubModules = 0;
    let totalClickModules = 0;
    let lastCreated: Date | undefined;
    
    modules.forEach(module => {
      if (module.subModules) {
        totalSubModules += module.subModules.length;
      }
      if (module.clickModules) {
        totalClickModules += module.clickModules.length;
      }
      
      const moduleDate = new Date(module.createdAt);
      if (!lastCreated || moduleDate > lastCreated) {
        lastCreated = moduleDate;
      }
    });
    
    return {
      totalModules: modules.length,
      totalSubModules,
      totalClickModules,
      lastCreated
    };
  }

  // 搜索模块
  searchModules(query: string): any[] {
    const modules = this.loadAllModules();
    const lowerQuery = query.toLowerCase();
    
    return modules.filter(module => 
      module.name.toLowerCase().includes(lowerQuery) ||
      (module.subModules && module.subModules.some((sub: any) => 
        sub.name.toLowerCase().includes(lowerQuery)
      )) ||
      (module.clickModules && module.clickModules.some((click: any) => 
        click.name.toLowerCase().includes(lowerQuery)
      ))
    );
  }

  // 获取文件夹结构预览
  getFolderStructurePreview(moduleName: string): string {
    const folderName = this.createSafeFolderName(moduleName);
    
    return `
${this.baseDir}/
└── ${folderName}/
    ├── main_screenshot.png     # 主界面截图
    ├── config.json            # 主界面配置
    ├── sub_module_1/          # 子模块文件夹
    │   ├── screenshot.png     # 子模块截图
    │   └── config.json        # 子模块配置
    ├── sub_module_2/
    │   ├── screenshot.png
    │   └── config.json
    └── click_modules/         # 点击模块文件夹
        ├── default_click.json # 默认点击配置
        └── custom_click.json  # 自定义点击配置
    `;
  }
}

// 导出单例实例
export const folderManager = FolderManager.getInstance();
