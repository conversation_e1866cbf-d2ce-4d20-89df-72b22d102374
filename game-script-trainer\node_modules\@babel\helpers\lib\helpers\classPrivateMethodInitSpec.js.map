{"version": 3, "names": ["_checkPrivateRedeclaration", "require", "_classPrivateMethodInitSpec", "obj", "privateSet", "checkPrivateRedeclaration", "add"], "sources": ["../../src/helpers/classPrivateMethodInitSpec.ts"], "sourcesContent": ["/* @minVersion 7.14.1 */\n\nimport checkPrivateRedeclaration from \"./checkPrivateRedeclaration.ts\";\n\nexport default function _classPrivateMethodInitSpec(\n  obj: object,\n  privateSet: WeakSet<object>,\n) {\n  checkPrivateRedeclaration(obj, privateSet);\n  privateSet.add(obj);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,0BAAA,GAAAC,OAAA;AAEe,SAASC,2BAA2BA,CACjDC,GAAW,EACXC,UAA2B,EAC3B;EACA,IAAAC,kCAAyB,EAACF,GAAG,EAAEC,UAAU,CAAC;EAC1CA,UAAU,CAACE,GAAG,CAACH,GAAG,CAAC;AACrB", "ignoreList": []}