# 游戏脚本训练器 - 真实ADB连接指南

## 🎯 概述

本应用现在支持真实的ADB连接和截图功能，不再使用任何模拟代码。通过ADB代理服务器，可以直接与红手指云手机进行真实的ADB通信。

## 📋 系统要求

### 必需软件
1. **Node.js** (v14+) - [下载地址](https://nodejs.org/)
2. **Android SDK Platform Tools** (包含ADB) - [下载地址](https://developer.android.com/studio/releases/platform-tools)
3. **红手指云手机** - 已启动并可通过ADB连接

### 环境配置
1. 确保ADB已添加到系统PATH环境变量
2. 在命令行中执行 `adb version` 验证ADB可用

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）
```bash
# 双击运行
start-with-adb.bat
```

### 方法2: 手动启动
```bash
# 1. 启动ADB代理服务器
node adb-proxy-server.js

# 2. 启动前端（新终端）
npm run dev
```

## 🔧 ADB连接配置

### 1. 连接红手指云手机
```bash
# 连接到红手指云手机
adb connect 127.0.0.1:59829

# 验证连接
adb devices
# 应该看到: 127.0.0.1:59829    device
```

### 2. 在应用中配置
- IP地址: `127.0.0.1`
- 端口: `59829`
- 点击"测试连接"验证
- 点击"连接"建立连接

## 📱 功能说明

### ADB代理服务器 (localhost:8080)
- **POST /shell** - 执行ADB Shell命令
- **POST /screenshot** - 获取真实截图
- **GET /device/info** - 获取设备信息
- **GET /devices** - 获取设备列表
- **POST /tap** - 执行点击操作

### 前端应用 (localhost:3000)
- **云手机连接** - 通过ADB代理连接
- **真实截图** - 获取设备真实截图
- **坐标识别** - 精确的坐标定位
- **界面识别** - 基于真实截图的识别
- **脚本工作流** - 完整的自动化脚本

## 🔍 故障排除

### 连接失败
1. **检查ADB连接**
   ```bash
   adb devices
   ```
   
2. **重新连接设备**
   ```bash
   adb disconnect 127.0.0.1:59829
   adb connect 127.0.0.1:59829
   ```

3. **重启ADB服务**
   ```bash
   adb kill-server
   adb start-server
   ```

### 截图失败
1. **检查设备权限**
   ```bash
   adb -s 127.0.0.1:59829 shell screencap -p > test.png
   ```

2. **检查代理服务器**
   - 确保 http://localhost:8080 可访问
   - 查看代理服务器控制台日志

### 代理服务器无法启动
1. **检查端口占用**
   ```bash
   netstat -an | findstr 8080
   ```

2. **更换端口**
   - 修改 `adb-proxy-server.js` 中的 PORT 变量
   - 修改前端代码中的代理服务器地址

## 📝 开发说明

### 项目结构
```
game-script-trainer/
├── src/                    # 前端源码
├── adb-proxy-server.js     # ADB代理服务器
├── start-with-adb.bat      # 启动脚本
└── ADB-README.md          # 本文档
```

### 核心文件
- `src/utils/cloudPhoneApi.ts` - ADB API接口
- `src/components/CloudPhoneConnection.tsx` - 连接组件
- `adb-proxy-server.js` - ADB代理服务器

### 自定义配置
可以修改以下配置：
- ADB代理服务器端口 (默认: 8080)
- 默认设备ID (默认: 127.0.0.1:59829)
- 截图格式和质量
- 连接超时时间

## 🎮 使用流程

1. **启动服务** - 运行 `start-with-adb.bat`
2. **连接设备** - `adb connect 127.0.0.1:59829`
3. **配置应用** - 输入IP端口并连接
4. **获取截图** - 点击截图按钮获取真实截图
5. **创建脚本** - 基于真实截图创建自动化脚本

## ⚠️ 注意事项

1. **ADB权限** - 确保ADB有足够权限访问设备
2. **网络连接** - 确保设备网络连接稳定
3. **防火墙** - 确保端口8080和3000未被阻止
4. **设备状态** - 确保红手指云手机处于可用状态

现在你拥有了完整的真实ADB连接和截图功能！🚀
