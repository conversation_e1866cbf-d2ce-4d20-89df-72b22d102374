import React, { useState, useEffect, useCallback } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Tabs,
  Tab,
  Paper,
  Fab,
  Badge
} from '@mui/material';
import {
  PhoneAndroid,
  CameraAlt,
  TouchApp,
  ViewModule,
  Settings,
  Save
} from '@mui/icons-material';

import CloudPhoneConnection from './components/CloudPhoneConnection';
import ScreenshotCapture from './components/ScreenshotCapture';
import MouseInteraction from './components/MouseInteraction';
import InterfaceRecognition from './components/InterfaceRecognition';
import GameScriptWorkflow from './components/GameScriptWorkflow';

import {
  AppState,
  CloudPhoneConnection as CloudPhoneConnectionType,
  MainModule,
  MousePaintArea,
  AreaSelection,
  ClickModule,
  WorkflowState
} from './types';
import {
  loadMainModules,
  saveMainModules
} from './utils/dataManager';
import { folderManager } from './utils/folderManager';

// 创建主题
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
});

// 标签页接口
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function App() {
  console.log('App组件正在渲染...');
  const [currentTab, setCurrentTab] = useState(0);
  // 从localStorage加载云手机连接配置
  const loadCloudPhoneConfig = useCallback(() => {
    try {
      const saved = localStorage.getItem('cloudPhoneConfig');
      if (saved) {
        const config = JSON.parse(saved);
        return {
          ip: config.ip || '*************',
          port: config.port || 5555,
          isConnected: false // 刷新后总是断开连接
        };
      }
    } catch (error) {
      console.error('加载云手机配置失败:', error);
    }
    return {
      ip: '*************',
      port: 5555,
      isConnected: false
    };
  }, []);

  const [appState, setAppState] = useState<AppState>({
    cloudPhone: loadCloudPhoneConfig(),
    mainModules: [],
    mousePaintAreas: [],
    isCapturing: false,
    isRecognizing: false,
    workflow: {
      currentStep: 'screenshot',
      subAreaSelections: [],
      clickModules: [],
      isProcessing: false
    }
  });

  // 加载保存的数据
  useEffect(() => {
    const savedModules = loadMainModules();
    setAppState(prev => ({
      ...prev,
      mainModules: savedModules
    }));
  }, []);

  // 自动保存数据
  useEffect(() => {
    const saveTimer = setInterval(() => {
      saveMainModules(appState.mainModules);
    }, 30000); // 每30秒自动保存

    return () => clearInterval(saveTimer);
  }, [appState.mainModules]);

  // 处理标签页切换
  const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  }, []);

  // 处理云手机连接状态变化
  const handleConnectionChange = useCallback((connection: CloudPhoneConnectionType) => {
    setAppState(prev => ({
      ...prev,
      cloudPhone: connection
    }));

    // 保存IP和端口配置到localStorage（不保存连接状态）
    try {
      const configToSave = {
        ip: connection.ip,
        port: connection.port
      };
      localStorage.setItem('cloudPhoneConfig', JSON.stringify(configToSave));
    } catch (error) {
      console.error('保存云手机配置失败:', error);
    }
  }, []);

  // 处理截图捕获
  const handleScreenshotCapture = useCallback((screenshot: string) => {
    setAppState(prev => ({
      ...prev,
      currentScreenshot: screenshot
    }));
  }, []);

  // 处理主模块变化
  const handleModulesChange = useCallback((modules: MainModule[]) => {
    setAppState(prev => ({
      ...prev,
      mainModules: modules
    }));
    
    // 立即保存
    saveMainModules(modules);
  }, []);

  // 处理鼠标涂抹区域识别
  const handleAreaIdentified = useCallback((area: MousePaintArea) => {
    setAppState(prev => ({
      ...prev,
      mousePaintAreas: [...prev.mousePaintAreas, area]
    }));
  }, []);

  // 删除鼠标涂抹区域
  const handleAreaDelete = useCallback((areaId: string) => {
    setAppState(prev => ({
      ...prev,
      mousePaintAreas: prev.mousePaintAreas.filter(area => area.id !== areaId)
    }));
  }, []);

  // 清除所有鼠标涂抹区域
  const handleAreasClear = useCallback(() => {
    setAppState(prev => ({
      ...prev,
      mousePaintAreas: []
    }));
  }, []);

  // 处理工作流程完成
  const handleWorkflowComplete = useCallback(async (data: {
    mainArea: AreaSelection;
    subAreas: AreaSelection[];
    clickModules: ClickModule[];
  }) => {
    try {
      // 保存到文件夹系统
      const result = await folderManager.saveMainModule(
        data.mainArea,
        data.subAreas,
        data.clickModules
      );

      if (result.success) {
        // 转换为MainModule格式并添加到应用状态
        const newMainModule: MainModule = {
          id: data.mainArea.id,
          name: data.mainArea.name,
          folderName: result.folderPath.split('/').pop() || data.mainArea.name,
          priority: appState.mainModules.length,
          screenshot: data.mainArea.screenshot,
          subModules: data.subAreas.map(sub => ({
            id: sub.id,
            name: sub.name,
            folderName: sub.name.replace(/[^\w\u4e00-\u9fa5]/g, '_').toLowerCase(),
            color: sub.color || { hex: '#000000', rgb: { r: 0, g: 0, b: 0 }, hsl: { h: 0, s: 0, l: 0 } },
            coordinate: { x: sub.coordinates.x, y: sub.coordinates.y },
            screenshot: sub.screenshot,
            textInfo: sub.text,
            createdAt: sub.createdAt,
            updatedAt: sub.createdAt
          })),
          createdAt: data.mainArea.createdAt,
          updatedAt: data.mainArea.createdAt,
          isActive: false
        };

        setAppState(prev => ({
          ...prev,
          mainModules: [...prev.mainModules, newMainModule]
        }));

        // 保存到localStorage
        saveMainModules([...appState.mainModules, newMainModule]);

        alert(`模块创建成功！\n文件夹路径: ${result.folderPath}`);
      } else {
        alert(`保存失败: ${result.error}`);
      }
    } catch (error) {
      console.error('工作流程完成处理失败:', error);
      alert('保存模块时发生错误');
    }
  }, [appState.mainModules]);

  // 手动保存
  const handleManualSave = useCallback(() => {
    saveMainModules(appState.mainModules);
    // 这里可以添加保存成功的提示
    console.log('数据已保存');
  }, [appState.mainModules]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      
      {/* 应用栏 */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            游戏脚本训练器
          </Typography>
          
          {/* 连接状态指示器 */}
          <Box display="flex" alignItems="center" gap={2}>
            <Badge
              color={appState.cloudPhone.isConnected ? 'success' : 'error'}
              variant="dot"
            >
              <PhoneAndroid />
            </Badge>
            
            <Typography variant="body2">
              {appState.cloudPhone.isConnected 
                ? `已连接 ${appState.cloudPhone.ip}:${appState.cloudPhone.port}`
                : '未连接'
              }
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* 主要内容区域 */}
      <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
        <Paper elevation={2}>
          {/* 标签页导航 */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={currentTab} 
              onChange={handleTabChange}
              variant="fullWidth"
              textColor="primary"
              indicatorColor="primary"
            >
              <Tab
                icon={<PhoneAndroid />}
                label="云手机连接"
                id="tab-0"
                aria-controls="tabpanel-0"
              />
              <Tab
                icon={<Settings />}
                label="脚本工作流程"
                id="tab-1"
                aria-controls="tabpanel-1"
              />
              <Tab
                icon={<CameraAlt />}
                label="截图功能"
                id="tab-2"
                aria-controls="tabpanel-2"
              />
              <Tab
                icon={<TouchApp />}
                label="鼠标交互"
                id="tab-3"
                aria-controls="tabpanel-3"
              />
              <Tab
                icon={
                  <Badge badgeContent={appState.mainModules.length} color="primary">
                    <ViewModule />
                  </Badge>
                }
                label="界面识别"
                id="tab-4"
                aria-controls="tabpanel-4"
              />
            </Tabs>
          </Box>

          {/* 标签页内容 */}
          <TabPanel value={currentTab} index={0}>
            <CloudPhoneConnection
              connection={appState.cloudPhone}
              onConnectionChange={handleConnectionChange}
            />
          </TabPanel>

          <TabPanel value={currentTab} index={1}>
            <GameScriptWorkflow
              connection={appState.cloudPhone}
              onWorkflowComplete={handleWorkflowComplete}
            />
          </TabPanel>

          <TabPanel value={currentTab} index={2}>
            <ScreenshotCapture
              connection={appState.cloudPhone}
              currentScreenshot={appState.currentScreenshot}
              onScreenshotCapture={handleScreenshotCapture}
            />
          </TabPanel>

          <TabPanel value={currentTab} index={3}>
            <MouseInteraction
              currentScreenshot={appState.currentScreenshot}
              onAreaIdentified={handleAreaIdentified}
              savedAreas={appState.mousePaintAreas}
              onAreaDelete={handleAreaDelete}
              onAreasClear={handleAreasClear}
            />
          </TabPanel>

          <TabPanel value={currentTab} index={4}>
            <InterfaceRecognition
              modules={appState.mainModules}
              onModulesChange={handleModulesChange}
              currentScreenshot={appState.currentScreenshot}
              onScreenshotCapture={handleScreenshotCapture}
            />
          </TabPanel>
        </Paper>
      </Container>

      {/* 浮动保存按钮 */}
      <Fab
        color="primary"
        aria-label="save"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={handleManualSave}
      >
        <Save />
      </Fab>

      {/* 状态栏 */}
      <Paper
        elevation={3}
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          p: 1,
          backgroundColor: 'background.paper',
          borderTop: 1,
          borderColor: 'divider'
        }}
      >
        <Container maxWidth="xl">
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary">
              主模块: {appState.mainModules.length} | 
              识别区域: {appState.mousePaintAreas.length} |
              当前截图: {appState.currentScreenshot ? '已获取' : '未获取'}
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              游戏脚本训练器 v1.0.0
            </Typography>
          </Box>
        </Container>
      </Paper>
    </ThemeProvider>
  );
}

export default App;
