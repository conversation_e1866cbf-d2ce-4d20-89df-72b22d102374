/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 禁用文本选择（在某些交互区域） */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 画布容器样式 */
.canvas-container {
  position: relative;
  overflow: hidden;
}

.canvas-container canvas {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  
  .MuiTabs-root {
    min-height: 48px;
  }
  
  .MuiTab-root {
    min-height: 48px;
    padding: 6px 12px;
  }
}

/* 自定义颜色变量 */
:root {
  --primary-color: #1976d2;
  --secondary-color: #dc004e;
  --success-color: #2e7d32;
  --warning-color: #ed6c02;
  --error-color: #d32f2f;
  --info-color: #0288d1;
  
  --background-default: #fafafa;
  --background-paper: #ffffff;
  
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.6);
  --text-disabled: rgba(0, 0, 0, 0.38);
}

/* 工具提示样式增强 */
.MuiTooltip-tooltip {
  font-size: 0.75rem !important;
  max-width: 300px !important;
}

/* 表格样式增强 */
.MuiTableContainer-root {
  border-radius: 8px !important;
}

.MuiTableRow-root:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

/* 卡片样式增强 */
.MuiCard-root {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.3s ease !important;
}

.MuiCard-root:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

/* 按钮样式增强 */
.MuiButton-root {
  border-radius: 8px !important;
  text-transform: none !important;
  font-weight: 500 !important;
}

/* 输入框样式增强 */
.MuiTextField-root {
  margin-bottom: 8px !important;
}

.MuiOutlinedInput-root {
  border-radius: 8px !important;
}

/* 芯片样式增强 */
.MuiChip-root {
  border-radius: 16px !important;
  font-weight: 500 !important;
}

/* 应用栏样式增强 */
.MuiAppBar-root {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
}

/* 标签页样式增强 */
.MuiTabs-indicator {
  height: 3px !important;
  border-radius: 3px !important;
}

.MuiTab-root {
  text-transform: none !important;
  font-weight: 500 !important;
  min-height: 64px !important;
}

/* 浮动按钮样式增强 */
.MuiFab-root {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.MuiFab-root:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

/* 对话框样式增强 */
.MuiDialog-paper {
  border-radius: 12px !important;
}

/* 警告框样式增强 */
.MuiAlert-root {
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* 进度条样式增强 */
.MuiLinearProgress-root {
  border-radius: 4px !important;
  height: 6px !important;
}

/* 滑块样式增强 */
.MuiSlider-thumb {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
}

/* 徽章样式增强 */
.MuiBadge-badge {
  font-weight: 600 !important;
  font-size: 0.75rem !important;
}

/* 分页样式增强 */
.MuiPagination-ul {
  justify-content: center;
}

/* 数据网格样式增强 */
.MuiDataGrid-root {
  border-radius: 8px !important;
  border: 1px solid rgba(224, 224, 224, 1) !important;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 打印样式 */
@media print {
  .MuiFab-root,
  .MuiAppBar-root,
  .no-print {
    display: none !important;
  }
  
  .MuiContainer-root {
    max-width: none !important;
    padding: 0 !important;
  }
}
