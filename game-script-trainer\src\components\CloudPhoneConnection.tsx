import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Chip,
  Grid,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  <PERSON>Android,
  Wifi,
  WifiOff,
  Refresh,
  Settings
} from '@mui/icons-material';
import { CloudPhoneConnection as CloudPhoneConnectionType } from '../types';
import { testCloudPhoneConnection } from '../utils/cloudPhoneApi';

interface CloudPhoneConnectionProps {
  connection: CloudPhoneConnectionType;
  onConnectionChange: (connection: CloudPhoneConnectionType) => void;
}

const CloudPhoneConnection: React.FC<CloudPhoneConnectionProps> = ({
  connection,
  onConnectionChange
}) => {
  const [ip, setIp] = useState(connection.ip);
  const [port, setPort] = useState(connection.port.toString());

  // 当props中的连接信息变化时，同步本地状态
  useEffect(() => {
    setIp(connection.ip);
    setPort(connection.port.toString());
  }, [connection.ip, connection.port]);

  // 保存配置到localStorage
  const saveConfig = useCallback((newIp: string, newPort: string) => {
    try {
      const configToSave = {
        ip: newIp,
        port: parseInt(newPort) || 5555
      };
      localStorage.setItem('cloudPhoneConfig', JSON.stringify(configToSave));
    } catch (error) {
      console.error('保存云手机配置失败:', error);
    }
  }, []);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<string>('');
  const [heartbeatInterval, setHeartbeatInterval] = useState<NodeJS.Timeout | null>(null);

  // 处理IP变化
  const handleIpChange = useCallback((newIp: string) => {
    setIp(newIp);
    saveConfig(newIp, port);
  }, [port, saveConfig]);

  // 处理端口变化
  const handlePortChange = useCallback((newPort: string) => {
    setPort(newPort);
    saveConfig(ip, newPort);
  }, [ip, saveConfig]);

  // 重置配置
  const handleResetConfig = useCallback(() => {
    if (window.confirm('确定要重置连接配置吗？')) {
      const defaultIp = '*************';
      const defaultPort = '5555';
      setIp(defaultIp);
      setPort(defaultPort);
      saveConfig(defaultIp, defaultPort);

      // 如果当前已连接，断开连接
      if (connection.isConnected) {
        handleDisconnect();
      }
    }
  }, [connection.isConnected, saveConfig]);

  // 验证IP地址格式
  const isValidIP = (ip: string): boolean => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  };

  // 验证端口号
  const isValidPort = (port: string): boolean => {
    const portNum = parseInt(port);
    return !isNaN(portNum) && portNum > 0 && portNum <= 65535;
  };

  // 连接云手机
  const handleConnect = useCallback(async () => {
    if (!isValidIP(ip)) {
      setConnectionError('请输入有效的IP地址');
      return;
    }

    if (!isValidPort(port)) {
      setConnectionError('请输入有效的端口号 (1-65535)');
      return;
    }

    setIsConnecting(true);
    setConnectionError('');
    setConnectionStatus('正在测试连接...');

    try {
      // 调用真实的云手机连接测试API
      setConnectionStatus(`正在连接 ${ip}:${port}...`);
      console.log(`尝试连接云手机: ${ip}:${port}`);

      const result = await testCloudPhoneConnection({
        ip,
        port: parseInt(port),
        timeout: 15000
      });

      console.log('连接测试结果:', result);

      if (result.success) {
        setConnectionStatus('连接成功，正在建立会话...');

        const now = new Date();
        const updatedConnection: CloudPhoneConnectionType = {
          ip,
          port: parseInt(port),
          isConnected: true,
          connectionTime: now,
          lastHeartbeat: now
        };

        onConnectionChange(updatedConnection);

        // 开始心跳检测
        startHeartbeat(ip, parseInt(port));

        setConnectionStatus('');
        console.log('云手机连接成功:', result.deviceInfo);
      } else {
        setConnectionError(`连接失败: ${result.message}`);
        setConnectionStatus('');
        console.error('连接测试失败:', result.message);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      setConnectionError(`连接失败: ${errorMessage}\n\n请检查：\n1. IP地址是否正确\n2. 端口是否正确\n3. 云手机是否启动\n4. 网络是否连通`);
      setConnectionStatus('');
      console.error('连接云手机失败:', error);
    } finally {
      setIsConnecting(false);
    }
  }, [ip, port, onConnectionChange]);

  // 断开连接
  const handleDisconnect = useCallback(() => {
    const updatedConnection: CloudPhoneConnectionType = {
      ...connection,
      isConnected: false,
      connectionTime: undefined,
      lastHeartbeat: undefined
    };

    onConnectionChange(updatedConnection);
    
    // 停止心跳检测
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      setHeartbeatInterval(null);
    }
  }, [connection, onConnectionChange, heartbeatInterval]);

  // 开始心跳检测
  const startHeartbeat = useCallback((currentIp: string, currentPort: number) => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
    }

    const interval = setInterval(async () => {
      try {
        // 发送真实的心跳检测，使用当前连接的IP和端口
        const result = await testCloudPhoneConnection({
          ip: currentIp,
          port: currentPort,
          timeout: 5000
        });

        if (result.success) {
          const updatedConnection: CloudPhoneConnectionType = {
            ip: currentIp,
            port: currentPort,
            isConnected: true,
            connectionTime: connection.connectionTime,
            lastHeartbeat: new Date()
          };
          onConnectionChange(updatedConnection);
        } else {
          console.error('心跳检测失败:', result.message);
          // 连接断开
          handleDisconnect();
        }
      } catch (error) {
        console.error('心跳检测失败:', error);
        // 连接断开
        handleDisconnect();
      }
    }, 30000); // 每30秒发送一次心跳

    setHeartbeatInterval(interval);
  }, [connection.connectionTime, onConnectionChange, heartbeatInterval, handleDisconnect]);

  // 组件卸载时清理心跳
  useEffect(() => {
    return () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
    };
  }, [heartbeatInterval]);

  // 测试连接
  const handleTestConnection = useCallback(async () => {
    if (!isValidIP(ip) || !isValidPort(port)) {
      setConnectionError('请输入有效的IP地址和端口号');
      return;
    }

    setIsConnecting(true);
    setConnectionError('');
    setConnectionStatus('正在测试连接...');

    try {
      // 调用真实的云手机连接测试API
      setConnectionStatus(`正在测试 ${ip}:${port}...`);
      console.log(`测试云手机连接: ${ip}:${port}`);

      const result = await testCloudPhoneConnection({
        ip,
        port: parseInt(port),
        timeout: 10000
      });

      console.log('测试结果:', result);

      setConnectionStatus('');

      if (result.success) {
        setConnectionError('');
        const deviceInfoText = result.deviceInfo ?
          `\n连接方式: ${result.message}\n设备信息: ${JSON.stringify(result.deviceInfo, null, 2)}` :
          `\n连接方式: ${result.message}`;
        alert(`连接测试成功！${deviceInfoText}`);
      } else {
        setConnectionError(result.message);
        alert(`连接测试失败: ${result.message}\n\n建议检查：\n1. IP地址是否正确\n2. 端口是否正确\n3. 云手机是否启动\n4. 网络是否连通`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setConnectionError(`连接测试失败: ${errorMessage}`);
      setConnectionStatus('');
      alert(`连接测试失败: ${errorMessage}\n\n请检查网络连接和云手机状态`);
    } finally {
      setIsConnecting(false);
    }
  }, [ip, port]);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        红手指云手机连接
      </Typography>

      <Grid container spacing={3}>
        {/* 连接配置 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                连接配置
              </Typography>

              <Alert severity="info" sx={{ mb: 2 }}>
                💾 IP和端口配置会自动保存，刷新页面后会自动恢复
              </Alert>
              
              <Box mb={2}>
                <TextField
                  fullWidth
                  label="IP地址"
                  value={ip}
                  onChange={(e) => handleIpChange(e.target.value)}
                  placeholder="*************"
                  error={ip !== '' && !isValidIP(ip)}
                  helperText={
                    connection.isConnected ? "连接时无法修改" :
                    ip !== '' && !isValidIP(ip) ? '请输入有效的IP地址' :
                    '配置会自动保存'
                  }
                  disabled={connection.isConnected}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label="端口号"
                  value={port}
                  onChange={(e) => handlePortChange(e.target.value)}
                  placeholder="5555"
                  error={port !== '' && !isValidPort(port)}
                  helperText={
                    connection.isConnected ? "连接时无法修改" :
                    port !== '' && !isValidPort(port) ? '请输入有效的端口号 (1-65535)' :
                    '配置会自动保存'
                  }
                  disabled={connection.isConnected}
                />
              </Box>

              {connectionError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {connectionError}
                </Alert>
              )}

              {connectionStatus && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  {connectionStatus}
                </Alert>
              )}

              <Box display="flex" gap={1}>
                {!connection.isConnected ? (
                  <>
                    <Button
                      variant="contained"
                      startIcon={isConnecting ? <CircularProgress size={20} /> : <Wifi />}
                      onClick={handleConnect}
                      disabled={isConnecting || !isValidIP(ip) || !isValidPort(port)}
                    >
                      {isConnecting ? '连接中...' : '连接'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Settings />}
                      onClick={handleTestConnection}
                      disabled={isConnecting || !isValidIP(ip) || !isValidPort(port)}
                    >
                      测试连接
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Refresh />}
                      onClick={handleResetConfig}
                      disabled={isConnecting}
                      color="secondary"
                    >
                      重置
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    color="error"
                    startIcon={<WifiOff />}
                    onClick={handleDisconnect}
                  >
                    断开连接
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 连接状态 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                连接状态
              </Typography>

              <Box display="flex" alignItems="center" mb={2}>
                <PhoneAndroid sx={{ mr: 1 }} />
                <Typography variant="body1" sx={{ mr: 2 }}>
                  设备状态:
                </Typography>
                <Chip
                  label={connection.isConnected ? '已连接' : '未连接'}
                  color={connection.isConnected ? 'success' : 'default'}
                  icon={connection.isConnected ? <Wifi /> : <WifiOff />}
                />
              </Box>

              {connection.isConnected && (
                <>
                  <Divider sx={{ my: 2 }} />
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    连接地址: {connection.ip}:{connection.port}
                  </Typography>
                  
                  {connection.connectionTime && (
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      连接时间: {connection.connectionTime.toLocaleString()}
                    </Typography>
                  )}
                  
                  {connection.lastHeartbeat && (
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      最后心跳: {connection.lastHeartbeat.toLocaleString()}
                    </Typography>
                  )}

                  <Box mt={2}>
                    <Alert severity="success">
                      云手机连接正常，可以开始使用脚本功能
                    </Alert>
                  </Box>
                </>
              )}

              {!connection.isConnected && (
                <Box mt={2}>
                  <Alert severity="info">
                    请配置并连接到红手指云手机后开始使用
                  </Alert>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 使用说明 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            使用说明
          </Typography>
          <Typography variant="body2" paragraph>
            1. 确保红手指云手机已启动并可以通过网络访问
          </Typography>
          <Typography variant="body2" paragraph>
            2. 在红手指应用中查看设备的IP地址和端口号
          </Typography>
          <Typography variant="body2" paragraph>
            3. 输入正确的IP地址和端口号，点击"测试连接"验证网络连通性
          </Typography>
          <Typography variant="body2" paragraph>
            4. 测试成功后点击"连接"建立与云手机的连接
          </Typography>
          <Typography variant="body2">
            5. 连接成功后即可使用截图、脚本执行等功能
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CloudPhoneConnection;
