import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  <PERSON><PERSON><PERSON>l,
  StepContent,
  Alert,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import {
  CameraAlt,
  CropFree,
  CreateNewFolder,
  TouchApp,
  CheckCircle,
  ArrowForward,
  ArrowBack
} from '@mui/icons-material';

import { 
  WorkflowStep, 
  WorkflowState, 
  AreaSelection, 
  ClickModule,
  CloudPhoneConnection 
} from '../types';
import { captureCloudPhoneScreen } from '../utils/cloudPhoneApi';
import AreaSelector from './AreaSelector';

interface GameScriptWorkflowProps {
  connection: CloudPhoneConnection;
  onWorkflowComplete: (data: {
    mainArea: AreaSelection;
    subAreas: AreaSelection[];
    clickModules: ClickModule[];
  }) => void;
}

const GameScriptWorkflow: React.FC<GameScriptWorkflowProps> = ({
  connection,
  onWorkflowComplete
}) => {
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    currentStep: 'screenshot',
    subAreaSelections: [],
    clickModules: [],
    isProcessing: false
  });

  const [showNameDialog, setShowNameDialog] = useState(false);
  const [currentName, setCurrentName] = useState('');
  const [currentDescription, setCurrentDescription] = useState('');

  // 步骤定义
  const steps = [
    { key: 'screenshot', label: '截图游戏界面', description: '获取当前游戏界面截图' },
    { key: 'select_main', label: '选择主界面区域', description: '用鼠标框选主界面识别区域' },
    { key: 'create_main', label: '创建主界面模块', description: '为主界面命名并保存' },
    { key: 'select_sub', label: '选择子界面模块', description: '选择子界面元素（可选）' },
    { key: 'create_sub', label: '创建子界面模块', description: '为子界面元素命名并保存' },
    { key: 'create_click', label: '创建点击模块', description: '创建默认点击位置' },
    { key: 'complete', label: '完成', description: '工作流程完成' }
  ];

  const currentStepIndex = steps.findIndex(step => step.key === workflowState.currentStep);

  // 步骤1: 截图游戏界面
  const handleScreenshot = useCallback(async () => {
    if (!connection.isConnected) {
      setWorkflowState(prev => ({ ...prev, error: '请先连接云手机' }));
      return;
    }

    setWorkflowState(prev => ({ ...prev, isProcessing: true, error: undefined }));

    try {
      const screenshot = await captureCloudPhoneScreen(
        { ip: connection.ip, port: connection.port, timeout: 15000 },
        { format: 'png', quality: 90 }
      );

      setWorkflowState(prev => ({
        ...prev,
        currentScreenshot: screenshot,
        currentStep: 'select_main',
        isProcessing: false
      }));
    } catch (error) {
      setWorkflowState(prev => ({
        ...prev,
        error: `截图失败: ${error instanceof Error ? error.message : '未知错误'}`,
        isProcessing: false
      }));
    }
  }, [connection]);

  // 步骤2: 选择主界面区域
  const handleMainAreaSelected = useCallback((selection: AreaSelection) => {
    setWorkflowState(prev => ({
      ...prev,
      mainAreaSelection: selection,
      currentStep: 'create_main'
    }));
  }, []);

  // 步骤3: 创建主界面模块
  const handleCreateMainModule = useCallback(() => {
    if (!workflowState.mainAreaSelection) return;

    setCurrentName('');
    setCurrentDescription('主界面识别区域');
    setShowNameDialog(true);
  }, [workflowState.mainAreaSelection]);

  const handleMainModuleNamed = useCallback(() => {
    if (!currentName.trim() || !workflowState.mainAreaSelection) return;

    const updatedSelection = {
      ...workflowState.mainAreaSelection,
      name: currentName.trim()
    };

    setWorkflowState(prev => ({
      ...prev,
      mainAreaSelection: updatedSelection,
      currentStep: 'select_sub'
    }));

    setShowNameDialog(false);
    setCurrentName('');
  }, [currentName, workflowState.mainAreaSelection]);

  // 步骤4: 选择子界面模块
  const handleSubAreaSelected = useCallback((selection: AreaSelection) => {
    setWorkflowState(prev => ({
      ...prev,
      subAreaSelections: [...prev.subAreaSelections, selection],
      currentStep: 'create_sub'
    }));
  }, []);

  // 步骤5: 创建子界面模块
  const handleCreateSubModule = useCallback(() => {
    setCurrentName('');
    setCurrentDescription('子界面模块');
    setShowNameDialog(true);
  }, []);

  const handleSubModuleNamed = useCallback(() => {
    if (!currentName.trim()) return;

    const lastSubArea = workflowState.subAreaSelections[workflowState.subAreaSelections.length - 1];
    if (!lastSubArea) return;

    const updatedSubAreas = [...workflowState.subAreaSelections];
    updatedSubAreas[updatedSubAreas.length - 1] = {
      ...lastSubArea,
      name: currentName.trim()
    };

    setWorkflowState(prev => ({
      ...prev,
      subAreaSelections: updatedSubAreas,
      currentStep: 'select_sub' // 可以继续添加更多子模块
    }));

    setShowNameDialog(false);
    setCurrentName('');
  }, [currentName, workflowState.subAreaSelections]);

  // 跳过子模块，直接创建点击模块
  const handleSkipSubModules = useCallback(() => {
    setWorkflowState(prev => ({
      ...prev,
      currentStep: 'create_click'
    }));
  }, []);

  // 步骤6: 创建点击模块
  const handleCreateClickModule = useCallback(() => {
    setCurrentName('');
    setCurrentDescription('默认点击位置');
    setShowNameDialog(true);
  }, []);

  const handleClickModuleNamed = useCallback(() => {
    if (!currentName.trim() || !workflowState.mainAreaSelection) return;

    const clickModule: ClickModule = {
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      name: currentName.trim(),
      type: 'coordinate',
      coordinate: {
        x: workflowState.mainAreaSelection.coordinates.x + workflowState.mainAreaSelection.coordinates.width / 2,
        y: workflowState.mainAreaSelection.coordinates.y + workflowState.mainAreaSelection.coordinates.height / 2
      },
      description: currentDescription,
      parentModuleId: workflowState.mainAreaSelection.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setWorkflowState(prev => ({
      ...prev,
      clickModules: [...prev.clickModules, clickModule],
      currentStep: 'complete'
    }));

    setShowNameDialog(false);
    setCurrentName('');
  }, [currentName, currentDescription, workflowState.mainAreaSelection]);

  // 完成工作流程
  const handleCompleteWorkflow = useCallback(() => {
    if (!workflowState.mainAreaSelection) return;

    onWorkflowComplete({
      mainArea: workflowState.mainAreaSelection,
      subAreas: workflowState.subAreaSelections,
      clickModules: workflowState.clickModules
    });

    // 重置工作流程
    setWorkflowState({
      currentStep: 'screenshot',
      subAreaSelections: [],
      clickModules: [],
      isProcessing: false
    });
  }, [workflowState, onWorkflowComplete]);

  // 返回上一步
  const handlePreviousStep = useCallback(() => {
    const stepOrder: WorkflowStep[] = ['screenshot', 'select_main', 'create_main', 'select_sub', 'create_sub', 'create_click', 'complete'];
    const currentIndex = stepOrder.indexOf(workflowState.currentStep);
    
    if (currentIndex > 0) {
      const previousStep = stepOrder[currentIndex - 1];
      setWorkflowState(prev => ({
        ...prev,
        currentStep: previousStep,
        error: undefined
      }));
    }
  }, [workflowState.currentStep]);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        游戏脚本创建工作流程
      </Typography>

      {workflowState.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {workflowState.error}
        </Alert>
      )}

      <Stepper activeStep={currentStepIndex} orientation="vertical">
        {steps.map((step, index) => (
          <Step key={step.key}>
            <StepLabel>
              {step.label}
            </StepLabel>
            <StepContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {step.description}
              </Typography>

              {/* 步骤1: 截图 */}
              {step.key === 'screenshot' && workflowState.currentStep === 'screenshot' && (
                <Box mt={2}>
                  <Button
                    variant="contained"
                    startIcon={<CameraAlt />}
                    onClick={handleScreenshot}
                    disabled={workflowState.isProcessing || !connection.isConnected}
                  >
                    {workflowState.isProcessing ? '截图中...' : '开始截图'}
                  </Button>
                  {!connection.isConnected && (
                    <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                      请先在"云手机连接"标签页连接设备
                    </Typography>
                  )}
                </Box>
              )}

              {/* 步骤2: 选择主界面区域 */}
              {step.key === 'select_main' && workflowState.currentStep === 'select_main' && workflowState.currentScreenshot && (
                <Box mt={2}>
                  <AreaSelector
                    screenshot={workflowState.currentScreenshot}
                    onAreaSelected={handleMainAreaSelected}
                    selectionType="main"
                    instruction="请用鼠标拖拽选择主界面识别区域"
                  />
                </Box>
              )}

              {/* 步骤3: 创建主界面模块 */}
              {step.key === 'create_main' && workflowState.currentStep === 'create_main' && (
                <Box mt={2}>
                  <Button
                    variant="contained"
                    startIcon={<CreateNewFolder />}
                    onClick={handleCreateMainModule}
                  >
                    为主界面命名
                  </Button>
                  {workflowState.mainAreaSelection && (
                    <Paper sx={{ mt: 2, p: 2 }}>
                      <Typography variant="subtitle2">已选择区域:</Typography>
                      <Typography variant="body2">
                        坐标: ({workflowState.mainAreaSelection.coordinates.x}, {workflowState.mainAreaSelection.coordinates.y})
                      </Typography>
                      <Typography variant="body2">
                        大小: {workflowState.mainAreaSelection.coordinates.width} × {workflowState.mainAreaSelection.coordinates.height}
                      </Typography>
                    </Paper>
                  )}
                </Box>
              )}

              {/* 步骤4: 选择子界面模块 */}
              {step.key === 'select_sub' && workflowState.currentStep === 'select_sub' && workflowState.currentScreenshot && (
                <Box mt={2}>
                  <AreaSelector
                    screenshot={workflowState.currentScreenshot}
                    onAreaSelected={handleSubAreaSelected}
                    selectionType="sub"
                    instruction="选择子界面元素（按钮、文字等）"
                  />
                  <Box mt={2} display="flex" gap={2}>
                    <Button
                      variant="outlined"
                      onClick={handleSkipSubModules}
                    >
                      跳过子模块
                    </Button>
                  </Box>
                  {workflowState.subAreaSelections.length > 0 && (
                    <Paper sx={{ mt: 2, p: 2 }}>
                      <Typography variant="subtitle2">已创建的子模块:</Typography>
                      {workflowState.subAreaSelections.map((sub, index) => (
                        <Chip key={index} label={sub.name || `子模块 ${index + 1}`} sx={{ mr: 1, mt: 1 }} />
                      ))}
                    </Paper>
                  )}
                </Box>
              )}

              {/* 步骤5: 创建子界面模块 */}
              {step.key === 'create_sub' && workflowState.currentStep === 'create_sub' && (
                <Box mt={2}>
                  <Button
                    variant="contained"
                    startIcon={<CreateNewFolder />}
                    onClick={handleCreateSubModule}
                  >
                    为子模块命名
                  </Button>
                </Box>
              )}

              {/* 步骤6: 创建点击模块 */}
              {step.key === 'create_click' && workflowState.currentStep === 'create_click' && (
                <Box mt={2}>
                  <Button
                    variant="contained"
                    startIcon={<TouchApp />}
                    onClick={handleCreateClickModule}
                  >
                    创建默认点击模块
                  </Button>
                </Box>
              )}

              {/* 步骤7: 完成 */}
              {step.key === 'complete' && workflowState.currentStep === 'complete' && (
                <Box mt={2}>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    工作流程已完成！所有模块已创建并保存。
                  </Alert>
                  <Button
                    variant="contained"
                    startIcon={<CheckCircle />}
                    onClick={handleCompleteWorkflow}
                    color="success"
                  >
                    完成并保存
                  </Button>
                </Box>
              )}

              {/* 导航按钮 */}
              {index > 0 && workflowState.currentStep !== 'complete' && (
                <Box mt={2}>
                  <Button
                    startIcon={<ArrowBack />}
                    onClick={handlePreviousStep}
                    sx={{ mr: 1 }}
                  >
                    上一步
                  </Button>
                </Box>
              )}
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* 命名对话框 */}
      <Dialog open={showNameDialog} onClose={() => setShowNameDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {workflowState.currentStep === 'create_main' && '主界面模块命名'}
          {workflowState.currentStep === 'create_sub' && '子界面模块命名'}
          {workflowState.currentStep === 'create_click' && '点击模块命名'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="模块名称"
            fullWidth
            variant="outlined"
            value={currentName}
            onChange={(e) => setCurrentName(e.target.value)}
            placeholder="请输入模块名称"
          />
          <TextField
            margin="dense"
            label="描述"
            fullWidth
            variant="outlined"
            value={currentDescription}
            onChange={(e) => setCurrentDescription(e.target.value)}
            placeholder="请输入模块描述"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNameDialog(false)}>取消</Button>
          <Button 
            onClick={
              workflowState.currentStep === 'create_main' ? handleMainModuleNamed :
              workflowState.currentStep === 'create_sub' ? handleSubModuleNamed :
              handleClickModuleNamed
            }
            variant="contained"
            disabled={!currentName.trim()}
          >
            确定
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GameScriptWorkflow;
