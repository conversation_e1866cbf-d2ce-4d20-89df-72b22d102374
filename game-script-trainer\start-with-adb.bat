@echo off
echo 启动游戏脚本训练器（包含ADB代理服务器）
echo ==========================================

echo.
echo 1. 检查ADB是否可用...
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: ADB未安装或不在PATH中
    echo 请安装Android SDK Platform Tools并添加到PATH
    pause
    exit /b 1
)
echo ADB检查通过

echo.
echo 2. 检查Node.js是否可用...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Node.js未安装
    echo 请安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo Node.js检查通过

echo.
echo 3. 安装ADB代理服务器依赖...
if not exist node_modules_adb (
    echo 首次运行，安装依赖包...
    copy adb-proxy-package.json package_adb.json
    npm install --prefix . express cors
    mkdir node_modules_adb
    echo 依赖安装完成
)

echo.
echo 4. 启动ADB代理服务器...
start "ADB代理服务器" cmd /k "echo ADB代理服务器启动中... && node adb-proxy-server.js"

echo.
echo 5. 等待ADB代理服务器启动...
timeout /t 3 /nobreak >nul

echo.
echo 6. 启动前端开发服务器...
start "前端开发服务器" cmd /k "echo 前端开发服务器启动中... && npm run dev"

echo.
echo 7. 等待前端服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo ==========================================
echo 启动完成！
echo.
echo ADB代理服务器: http://localhost:8080
echo 前端应用: http://localhost:3000
echo.
echo 使用说明:
echo 1. 确保红手指云手机已启动
echo 2. 执行: adb connect 127.0.0.1:59829
echo 3. 在应用中输入: 127.0.0.1:59829
echo 4. 点击连接测试，然后连接
echo.
echo 按任意键打开浏览器...
pause >nul
start http://localhost:3000
