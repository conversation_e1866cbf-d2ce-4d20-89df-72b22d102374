import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Grid,
  Chip,
  Divider,
  Alert,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Colorize,
  TextFields,
  Delete,
  Save,
  Undo,
  PanTool,
  HighlightAlt,
  FormatColorFill
} from '@mui/icons-material';
import * as fabric from 'fabric';
import { Coordinate, ColorInfo, MousePaintArea } from '../types';
import {
  getColorFromCoordinate,
  createCanvasFromBase64,
  processMousePaintArea,
  calculateBoundingBox
} from '../utils/imageProcessor';

interface MouseInteractionProps {
  currentScreenshot?: string;
  onAreaIdentified: (area: MousePaintArea) => void;
  savedAreas: MousePaintArea[];
  onAreaDelete: (areaId: string) => void;
  onAreasClear: () => void;
}

const MouseInteraction: React.FC<MouseInteractionProps> = ({
  currentScreenshot,
  onAreaIdentified,
  savedAreas,
  onAreaDelete,
  onAreasClear
}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawMode, setDrawMode] = useState<'free' | 'rect'>('free');
  const [currentCoordinates, setCurrentCoordinates] = useState<Coordinate[]>([]);
  const [currentColor, setCurrentColor] = useState<ColorInfo | null>(null);
  const [hoveredColor, setHoveredColor] = useState<ColorInfo | null>(null);
  const [hoveredCoordinate, setHoveredCoordinate] = useState<Coordinate | null>(null);
  const [selectedArea, setSelectedArea] = useState<MousePaintArea | null>(null);

  // 初始化Fabric.js画布
  useEffect(() => {
    if (currentScreenshot && containerRef.current) {
      // 清理旧的画布
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
      }

      // 创建新的画布
      const container = containerRef.current;
      const canvas = new fabric.Canvas('drawing-canvas', {
        isDrawingMode: true,
        width: container.clientWidth,
        height: 400
      });
      
      fabricCanvasRef.current = canvas;
      canvasRef.current = canvas.getElement() as HTMLCanvasElement;

      // 设置自由绘制模式
      const freeDrawingBrush = new fabric.PencilBrush(canvas);
      freeDrawingBrush.width = 3;
      freeDrawingBrush.color = 'rgba(255, 0, 0, 0.5)';
      canvas.freeDrawingBrush = freeDrawingBrush;

      // 加载背景图片
      fabric.Image.fromURL(currentScreenshot).then((img: any) => {
        // 调整图片大小以适应画布
        const canvasRatio = canvas.width! / canvas.height!;
        const imgRatio = img.width! / img.height!;

        let scaleFactor;
        if (canvasRatio > imgRatio) {
          scaleFactor = canvas.height! / img.height!;
        } else {
          scaleFactor = canvas.width! / img.width!;
        }

        img.scale(scaleFactor);

        // 设置为背景
        canvas.backgroundImage = img;
        canvas.renderAll();
      });

      // 监听鼠标移动事件获取颜色
      canvas.on('mouse:move', (options: any) => {
        if (!options.pointer) return;
        
        const pointer = options.pointer;
        const coordinate = { x: Math.round(pointer.x), y: Math.round(pointer.y) };
        setHoveredCoordinate(coordinate);
        
        try {
          // 获取当前坐标的颜色
          const ctx = canvas.getElement().getContext('2d');
          if (ctx) {
            const imageData = ctx.getImageData(coordinate.x, coordinate.y, 1, 1);
            const [r, g, b] = imageData.data;
            
            // 转换为十六进制
            const hex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
            
            // 转换为HSL
            const hsl = {
              h: 0,
              s: 0,
              l: 0
            };
            
            setHoveredColor({
              hex,
              rgb: { r, g, b },
              hsl
            });
          }
        } catch (error) {
          console.error('获取颜色失败:', error);
        }
      });

      // 监听路径创建完成事件
      canvas.on('path:created', async (e: any) => {
        const path = e.path;
        if (!path) return;
        
        // 获取路径的点
        const points = path.path;
        if (!points || points.length < 2) return;
        
        // 转换为坐标数组
        const coordinates: Coordinate[] = [];
        for (let i = 1; i < points.length; i++) {
          const point = points[i];
          if (point[0] === 'L' || point[0] === 'M') {
            coordinates.push({ x: Math.round(point[1]), y: Math.round(point[2]) });
          }
        }
        
        if (coordinates.length > 0) {
          setCurrentCoordinates(coordinates);
          
          // 处理绘制区域
          try {
            const canvasElement = canvas.getElement();
            const area = await processMousePaintArea(coordinates, canvasElement);
            setCurrentColor(area.color);
            onAreaIdentified(area);
          } catch (error) {
            console.error('处理绘制区域失败:', error);
          }
        }
      });

      return () => {
        if (fabricCanvasRef.current) {
          fabricCanvasRef.current.dispose();
        }
      };
    }
  }, [currentScreenshot, onAreaIdentified]);

  // 切换绘制模式
  const toggleDrawMode = useCallback(() => {
    if (!fabricCanvasRef.current) return;
    
    const canvas = fabricCanvasRef.current;
    
    if (drawMode === 'free') {
      // 切换到矩形模式
      canvas.isDrawingMode = false;
      setDrawMode('rect');
    } else {
      // 切换到自由绘制模式
      canvas.isDrawingMode = true;
      setDrawMode('free');
    }
  }, [drawMode]);

  // 清除当前绘制
  const handleClearDrawing = useCallback(() => {
    if (!fabricCanvasRef.current) return;
    
    const canvas = fabricCanvasRef.current;
    canvas.clear();
    
    // 重新设置背景图片
    if (currentScreenshot) {
      fabric.Image.fromURL(currentScreenshot).then((img: any) => {
        const canvasRatio = canvas.width! / canvas.height!;
        const imgRatio = img.width! / img.height!;

        let scaleFactor;
        if (canvasRatio > imgRatio) {
          scaleFactor = canvas.height! / img.height!;
        } else {
          scaleFactor = canvas.width! / img.width!;
        }

        img.scale(scaleFactor);

        canvas.backgroundImage = img;
        canvas.renderAll();
      });
    }
    
    setCurrentCoordinates([]);
    setCurrentColor(null);
    setSelectedArea(null);
  }, [currentScreenshot]);

  // 选择区域
  const handleSelectArea = useCallback((area: MousePaintArea) => {
    setSelectedArea(area);
  }, []);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        鼠标交互与坐标识别
      </Typography>

      {!currentScreenshot && (
        <Alert severity="info" sx={{ mb: 3 }}>
          请先获取游戏截图后再进行鼠标交互操作
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* 绘制区域 */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  涂抹区域
                </Typography>
                
                <Box display="flex" gap={1}>
                  <Tooltip title={drawMode === 'free' ? '自由绘制模式' : '矩形选择模式'}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={drawMode === 'free' ? <PanTool /> : <HighlightAlt />}
                      onClick={toggleDrawMode}
                      disabled={!currentScreenshot}
                    >
                      {drawMode === 'free' ? '自由绘制' : '矩形选择'}
                    </Button>
                  </Tooltip>
                  
                  <Tooltip title="清除当前绘制">
                    <Button
                      variant="outlined"
                      size="small"
                      color="error"
                      startIcon={<Delete />}
                      onClick={handleClearDrawing}
                      disabled={!currentScreenshot}
                    >
                      清除
                    </Button>
                  </Tooltip>
                </Box>
              </Box>

              <Paper
                ref={containerRef}
                sx={{
                  width: '100%',
                  height: '400px',
                  overflow: 'hidden',
                  position: 'relative',
                  backgroundColor: '#f5f5f5',
                  border: '1px solid #ddd'
                }}
              >
                {currentScreenshot ? (
                  <canvas id="drawing-canvas" />
                ) : (
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    height="100%"
                    color="text.secondary"
                  >
                    <Typography>
                      请先获取游戏截图
                    </Typography>
                  </Box>
                )}
              </Paper>

              {hoveredCoordinate && (
                <Box mt={2} display="flex" gap={2} alignItems="center">
                  <Chip
                    label={`X: ${hoveredCoordinate.x}, Y: ${hoveredCoordinate.y}`}
                    variant="outlined"
                    size="small"
                  />
                  
                  {hoveredColor && (
                    <>
                      <Box
                        width={24}
                        height={24}
                        sx={{
                          backgroundColor: hoveredColor.hex,
                          border: '1px solid #ddd',
                          borderRadius: '4px'
                        }}
                      />
                      
                      <Chip
                        label={hoveredColor.hex}
                        variant="outlined"
                        size="small"
                      />
                      
                      <Chip
                        label={`RGB(${hoveredColor.rgb.r}, ${hoveredColor.rgb.g}, ${hoveredColor.rgb.b})`}
                        variant="outlined"
                        size="small"
                      />
                    </>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 识别结果 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                识别结果
              </Typography>

              {selectedArea && (
                <Box mb={3}>
                  <Typography variant="subtitle1" gutterBottom>
                    选中区域详情
                  </Typography>
                  
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Typography variant="body2">颜色:</Typography>
                    <Box
                      width={20}
                      height={20}
                      sx={{
                        backgroundColor: selectedArea.color.hex,
                        border: '1px solid #ddd',
                        borderRadius: '4px'
                      }}
                    />
                    <Typography variant="body2">{selectedArea.color.hex}</Typography>
                  </Box>
                  
                  <Typography variant="body2" gutterBottom>
                    坐标范围: X({selectedArea.boundingBox.x} - {selectedArea.boundingBox.x + selectedArea.boundingBox.width}), 
                    Y({selectedArea.boundingBox.y} - {selectedArea.boundingBox.y + selectedArea.boundingBox.height})
                  </Typography>
                  
                  {selectedArea.text && (
                    <Box mt={1}>
                      <Typography variant="body2" gutterBottom>
                        识别文字: {selectedArea.text.text}
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        置信度: {(selectedArea.text.confidence * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                  )}
                  
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    startIcon={<Delete />}
                    onClick={() => {
                      onAreaDelete(selectedArea.id);
                      setSelectedArea(null);
                    }}
                    sx={{ mt: 1 }}
                  >
                    删除此区域
                  </Button>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="subtitle1">
                  已保存区域 ({savedAreas.length})
                </Typography>
                
                {savedAreas.length > 0 && (
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    startIcon={<Delete />}
                    onClick={onAreasClear}
                  >
                    清除全部
                  </Button>
                )}
              </Box>

              {savedAreas.length > 0 ? (
                <TableContainer component={Paper} sx={{ maxHeight: 300, overflow: 'auto' }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>颜色</TableCell>
                        <TableCell>坐标</TableCell>
                        <TableCell>文字</TableCell>
                        <TableCell>操作</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {savedAreas.map((area) => (
                        <TableRow 
                          key={area.id}
                          hover
                          selected={selectedArea?.id === area.id}
                          onClick={() => handleSelectArea(area)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Box
                                width={16}
                                height={16}
                                sx={{
                                  backgroundColor: area.color.hex,
                                  border: '1px solid #ddd',
                                  borderRadius: '2px'
                                }}
                              />
                              <Typography variant="caption">
                                {area.color.hex}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Tooltip title={`X: ${area.boundingBox.x}, Y: ${area.boundingBox.y}, W: ${area.boundingBox.width}, H: ${area.boundingBox.height}`}>
                              <Typography variant="caption">
                                {area.boundingBox.x},{area.boundingBox.y}
                              </Typography>
                            </Tooltip>
                          </TableCell>
                          <TableCell>
                            <Typography variant="caption">
                              {area.text?.text || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation();
                                onAreaDelete(area.id);
                                if (selectedArea?.id === area.id) {
                                  setSelectedArea(null);
                                }
                              }}
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info">
                  在截图上涂抹区域以识别坐标、颜色和文字
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 使用说明 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            使用说明
          </Typography>
          <Typography variant="body2" paragraph>
            • 在截图上用鼠标涂抹或框选需要识别的区域
          </Typography>
          <Typography variant="body2" paragraph>
            • 系统会自动识别该区域的坐标、颜色和文字内容
          </Typography>
          <Typography variant="body2" paragraph>
            • 识别结果会显示在右侧面板中，可以点击查看详情
          </Typography>
          <Typography variant="body2">
            • 识别的区域可以用于创建脚本点击位置或文字识别条件
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MouseInteraction;
