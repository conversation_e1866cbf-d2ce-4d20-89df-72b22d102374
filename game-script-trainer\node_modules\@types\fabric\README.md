# Installation
> `npm install --save @types/fabric`

# Summary
This package contains type definitions for fabric (http://fabricjs.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/fabric.

### Additional Details
 * Last updated: <PERSON><PERSON>, 25 Feb 2025 00:03:48 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/oklemencic), [<PERSON>](https://github.com/joewashear007), [<PERSON>](https://github.com/mrand01), [<PERSON>](https://github.com/NotWoods), [<PERSON>](https://github.com/bmartinson), [<PERSON><PERSON>](https://github.com/RogerioTeixeira), [<PERSON>](https://github.com/BradleyHill), [<PERSON>](https://github.com/bmkrol823), [<PERSON>](https://github.com/glenngartner), [Codertx](https://github.com/codertx), [<PERSON>](https://github.com/mike667), [<PERSON>](https://gith<PERSON>.com/nataliemarleny), and [<PERSON> <PERSON><PERSON>ahalim](https://github.com/oxwazz).
